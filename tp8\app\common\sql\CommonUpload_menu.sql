-- 添加用户上传管理菜单


-- 添加用户上传管理主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `name`, `component`, `icon`, `auth`, `status`, `is_hide`, `sorted`, `create_time`, `update_time`) 
VALUES ('用户上传管理', 132, '2', '/common/commonUpload/index', 'commonUploadPage', 'common/commonUpload/index', 'Menu', 'common:commonUpload:listPage', '1', '1', 999, NOW(), NOW());

-- 获取插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加用户上传管理按钮权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `menu_type`, `auth`, `status`, `is_hide`,`create_time`, `update_time`) VALUES
('查询', @menuId, '3', 'common:commonUpload:listPage', '1','0', NOW(), NOW()),
('获取详情', @menuId, '3', 'common:commonUpload:getById', '1','0', NOW(), NOW()),
('获取排序', @menuId, '3', 'common:commonUpload:getSorted', '1','0', NOW(), NOW()),
('新增', @menuId, '3', 'common:commonUpload:add', '1','0', NOW(), NOW()),
('修改', @menuId, '3', 'common:commonUpload:update', '1','0', NOW(), NOW()),
('删除', @menuId, '3', 'common:commonUpload:deleteById', '1','0', NOW(), NOW()),
('批量删除', @menuId, '3', 'common:commonUpload:batchDelete', '1','0', NOW(), NOW()),
('更新状态', @menuId, '3', 'common:commonUpload:updateStatus', '1','0', NOW(), NOW());