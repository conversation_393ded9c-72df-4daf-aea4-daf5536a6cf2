<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysMenuModel;
use app\dogadmin\model\SysUserRoleModel;
use app\dogadmin\model\SysRoleMenuModel;

class SysMenuService extends BaseService
{

    public function __construct()
    {
        $this->model = new SysMenuModel();
    }

    /**
     * 根据角色ID获取权限按钮标识
     * @param array $roleIds 角色ID数组
     * @return array 权限按钮标识数组
     */
    public function getButtonsByRoleIds($roleIds)
    {
        if (empty($roleIds)) {
            return [];
        }

        // 检查是否包含超级管理员角色(role_id = 1)
        if (in_array(1, $roleIds)) {
            // 超级管理员返回所有权限按钮
            return ['*'];
//            return $this->model
//                ->where('status', '1')
//                ->where('auth', '<>', '')
//                ->whereNotNull('auth')
//                ->column('auth');
        }

        // 普通角色，获取角色对应的菜单ID
        $menuIds = (new SysRoleMenuModel())->whereIn('role_id', $roleIds)->column('menu_id');

        if (empty($menuIds)) {
            return [];
        }

        // 获取菜单的权限标识
        return $this->model->whereIn('id', array_unique($menuIds))
            ->where('status', '1')
            ->where('auth', '<>', '')
            ->whereNotNull('auth')
            ->column('auth');
    }

    public function listRouters($params)
    {
        $user_id = $params['admin_id'];
        $userRoles = $params['role_ids'];
        // 获取用户角色
//        $userRoleModel = new SysUserRoleModel();
//        $userRoles = $userRoleModel->where('user_id', $user_id)->column('role_id');

        if (empty($userRoles)) {
            return [];
        }
        
        // 检查是否为超级管理员 (role_id = 1)
        if (in_array(1, $userRoles)) {
            // 超级管理员，获取全部菜单
            $menus = $this->model
                ->where('status', '1')
                ->order('sorted', 'desc')
                ->select()
                ->toArray();
        } else {
            // 普通用户，根据角色权限获取菜单
            $roleMenuModel = new SysRoleMenuModel();
            
            // 获取角色对应的菜单ID
            $menuIds = $roleMenuModel
                ->whereIn('role_id', $userRoles)
                ->column('menu_id');
            
            if (empty($menuIds)) {
                return [];
            }
            
            // 根据菜单ID获取菜单信息
            $menus = $this->model
                ->whereIn('id', array_unique($menuIds))
                ->where('status', '1')
                ->order('sorted', 'asc')
                ->select()
                ->toArray();
        }
        
        return $menus;
    }

    public function listMenuNormal(){
        $res = $this->model->select()->toArray();
        return $res;
    }
    public function cascaderList()
    {
        $field = 'id as value, menu_name as  label, parent_id';
        $res = $this->model->field($field)->order(['sorted' => 'desc'])->select()->toArray();
        return $res;
    }

    public function addButtonAuth($params){
        $fun = [
            ['function_name'=>'listPage','function_desc'=>'列表'],
            ['function_name'=>'getById','function_desc'=>'详情'],
            ['function_name'=>'add','function_desc'=>'添加'],
            ['function_name'=>'deleteById','function_desc'=>'删除'],
            ['function_name'=>'update','function_desc'=>'更新'],
            ['function_name'=>'updateStatus','function_desc'=>'更新状态'],
            ['function_name'=>'getSorted','function_desc'=>'排序']
        ];
        $dataAll = [];
        foreach ($fun as $k=>$v){
            $dataAll[] = [
                'parent_id'=>$params['parent_id'],
                'menu_name'=>$v['function_desc'],
                'auth'=>$params['module'].':'.$params['controller'].':'.$v['function_name'],
                'menu_type'=>3,
                'status'=>1,
                'sorted'=>$k+1,
                'is_hide'=>0
            ];
        }
        $where = [
            ['parent_id','=',$params['parent_id']],
            ['menu_type','=',3]
        ];
        $dbData = $this->model->field('id,parent_id,name,auth,menu_type,status,sorted')->where($where)->select()->toArray();

        //更新
        $updateData = [];
        foreach ($dataAll as $data) {
            $auth = $data['auth'];
            foreach ($dbData as $dbItem) {
                if ($dbItem['auth'] === $auth) {
                    if ($dbItem['parent_id'] !== $data['parent_id'] ||
                        $dbItem['menu_name'] !== $data['menu_name'] ||
                        $dbItem['status'] !== $data['status'] ||
                        $dbItem['sorted'] !== $data['sorted']) {
                        $updateData[] = [
                            'id' => $dbItem['id'],
                            'parent_id' => $data['parent_id'],
                            'menu_name' => $data['menu_name'],
                            'status' => $data['status'],
                            'sorted' => $data['sorted'],
                            'is_hide'=>0
                        ];
                    }
                    break;
                }
            }
        }
        // 批量更新
        if (!empty($updateData)) {
            $this->model->saveAll($updateData);
        }

        //插入
        $insertData = [];
        foreach ($dataAll as $data) {
            $auth = $data['auth'];
            $exists = false;
            foreach ($dbData as $dbItem) {
                if ($dbItem['auth'] === $auth) {
                    $exists = true;
                    break;
                }
            }
            if (!$exists) {
                $insertData[] = $data;
            }
        }

        // 批量插入
        if (!empty($insertData)) {
            $this->model->insertAll($insertData);
        }

        return 'ok';
    }
}