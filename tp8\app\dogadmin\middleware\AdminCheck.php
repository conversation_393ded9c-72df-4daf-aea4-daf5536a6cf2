<?php

namespace app\dogadmin\middleware;

use app\lib\exception\DebugException;
use app\lib\exception\dogadmin\AuthException;
use app\lib\exception\dogadmin\SysException;
use app\Request;
use Closure;
use thans\jwt\facade\JWTAuth;
use app\dogadmin\service\SysUserService;
use app\dogadmin\service\SysUserRoleService;

class AdminCheck
{
    public function handle(Request $request, Closure $next)
    {
        try {
            // 验证token
            $payload = JWTAuth::auth();
            $adminId = $payload['admin_id'];
            
            if (!$adminId) {
                throw new AuthException([], 2); // token验证失败
            }

            // 检查用户是否存在
            $user = (new SysUserService())->getAdminById($adminId);
            
            if (!$user) {
                throw new AuthException([], 6); // 账号不存在
            }

            // 检查用户状态
            if ($user['status'] !== '1') {
                throw new AuthException([], 5); // 账号已被禁用
            }


            // 将用户ID注入到请求对象中
            $request->admin_id = $adminId;
            $request->role_ids = (new SysUserRoleService())->getRoleIdsByUserId($adminId);

//            throw new DebugException($request->role_ids);
            
            return $next($request);
            
        } catch (\thans\jwt\exception\TokenExpiredException $e) {
            throw new AuthException([], 2); // token已过期
        } catch (\thans\jwt\exception\TokenInvalidException $e) {
            throw new AuthException([], 2); // token无效
        } catch (\Exception $e) {
            throw new AuthException([],1,  $e->getMessage()); // 其他token验证失败
        }
    }
}