<?php

namespace app\dogadmin\controller;

use app\dogadmin\service\SysMenuService;
use app\dogadmin\service\SysRoleService;
use app\dogadmin\service\SysUserRoleService;
use app\dogadmin\service\SysUserService;
use app\dogadmin\service\SysUserDeptService;
use app\dogadmin\common\Captcha;
use app\dogadmin\common\ApiResponse;
use thans\jwt\facade\JWTAuth;

class SysUser extends Base
{
    /**
     * @var SysUserService
     */
    protected $service;
    protected function initialize(): void
    {
        // 子类可以重写此方法以添加自定义初始化逻辑
        $this->service = new SysUserService();
        $this->params = $this->request->param();
    }
    /**
     * 获取验证码
     * @return \think\Response
     */
    public function captcha()
    {
        $captcha = new Captcha();
        $result = $captcha->generate();
        return ApiResponse::success($result, '获取验证码成功');
    }

    /**
     * 用户登录
     * @return \think\Response
     */
    public function login()
    {
        $params = $this->params;
        
        // 验证必要参数
        if (empty($params['login_name']) || empty($params['password'])) {
            return ApiResponse::paramError('用户名和密码不能为空');
        }
        if (empty($params['security_code']) || empty($params['code_key'])) {
            return ApiResponse::paramError('验证码不能为空');
        }

        // 验证验证码
        $captcha = new Captcha();
        if (!$captcha->verify($params['code_key'], $params['security_code'])) {
            return ApiResponse::paramError('验证码错误或已过期');
        }
        
        // 验证登录
        $adminUser = $this->service->login($params);
        if (!$adminUser) {
            return ApiResponse::paramError('用户名或密码不正确');
        }

        // 生成token
        $token = JWTAuth::builder(['admin_id' => $adminUser['id']]);
        $res = [
            'token_name' => 'Authorization',
            'token_value' => $token
        ];
        return ApiResponse::success($res, '登录成功');
    }
    public function originPassword(){
        $params = [
            'admin_id'=>1,
            'password'=>'d123456'
        ];
        $res = $this->service->originPassword($params);
        return ApiResponse::success($res,'修改成功');
    }
    public function getUserInfo(){
        $adminId = request()->admin_id;

        // 获取用户基本信息
        $userInfo = $this->service->getUserByWhere([
            ['id', '=', $adminId],
            ['status', '=', 1],
        ]);
        
        if (empty($userInfo)) {
            return ApiResponse::paramError('用户不存在或已被禁用');
        }
        
        // 获取用户角色ID
        // $userRoleService = new SysUserRoleService();
        // $roleIds = $userRoleService->getRoleIdsByUserId($adminId);

        $roleIds = request()->role_ids;
        
        // 获取角色代码
        $roleService = new SysRoleService();
        $roles = $roleService->getRoleCodesByIds($roleIds);
        
        // 获取权限按钮
        $menuService = new SysMenuService();
        $buttons = $menuService->getButtonsByRoleIds($roleIds);
        
        // 合并用户信息
        $userInfo['roles'] = $roles;
        $userInfo['buttons'] = $buttons;
        
        $res = [
            'buttons'=> $buttons,
            'user'=> $userInfo,
            'roles'=> $roles
        ];
        
        return ApiResponse::success($res,'获取成功');
    }

    public function getById()
    {
        $params = $this->params;
        if (empty($params['id'])) {
            return ApiResponse::paramError('缺少主键参数id');
        }
        $res = $this->service->getById($params);
        // $userRoleService = new SysUserRoleService();
        // $res['roleIds'] = $userRoleService->getRoleIdsByUserId($res['id']);
        $res['roleIds'] = request()->role_ids;
        return ApiResponse::success($res,'获取成功');
    }

    public function resetPwd()
    {
        $params = $this->params;
        $adminId = request()->admin_id;
        // 获取用户角色ID
        // $userRoleService = new SysUserRoleService();
        // $roleIds = $userRoleService->getRoleIdsByUserId($adminId);
        $roleIds = request()->role_ids;
        if (in_array(1, $roleIds)) {
            // 超级管理员 可以修改所有人的密码
            $params['admin_id'] = $params['id'];
        }else{
            // 普通管理员 只能修改自己的密码
            $params['admin_id'] = $adminId;
        }
        $res = $this->service->resetPwd($params);
        return ApiResponse::success($res,'重置成功');
    }

    public function updateUserPwd()
    {
        $params = $this->params;

    }

    public function getPersonalData()
    {
        $adminId = request()->admin_id;
        $roleIds = request()->role_ids;
        
        // 调用服务层方法获取用户个人资料
        $result = $this->service->getPersonalData($adminId, $roleIds);
        
        if (empty($result)) {
            return ApiResponse::paramError('用户不存在或已被禁用');
        }

        return ApiResponse::success($result, '获取成功');
    }

    public function updateBasicData()
    {
        $params = $this->params;
        $adminId = request()->admin_id;
        $params['admin_id'] = $adminId;
        $result = $this->service->updateBasicData($params);
        return ApiResponse::success($result, '修改成功');
    }

    public function update(){
        $params = $this->params;

        // 验证必要参数
        if (empty($params['id'])) {
            return ApiResponse::paramError('缺少用户ID参数');
        }

        // 获取当前操作用户的权限
        $adminId = request()->admin_id;
        $roleIds = request()->role_ids;

        // 检查权限：只有超级管理员或者修改自己的信息才允许
        if (!in_array(1, $roleIds) && $params['id'] != $adminId) {
            return ApiResponse::paramError('无权限修改其他用户信息');
        }

        try {
            // 开始事务
            $this->service->getModel()->startTrans();

            // 准备用户基本信息数据
            $userData = [
                'id' => $params['id'],
                'login_name' => $params['login_name'] ?? '',
                'user_name' => $params['user_name'] ?? '',
                'sex' => $params['sex'] ?? '3',
                'phone' => $params['phone'] ?? '',
                'email' => $params['email'] ?? '',
                'avatar' => $params['avatar'] ?? '',
                'remark' => $params['remark'] ?? '',
                'user_type' => $params['user_type'] ?? '1',
                'status' => $params['status'] ?? '1',
            ];

            // 如果传递了密码，则更新密码
            if (!empty($params['password']) && $params['password'] !== '359b4c7ff41a48a198a451cc464bc749') {
                $userData['password'] = md5_salt($params['password']);
                $userData['pwd_update_time'] = date('Y-m-d H:i:s');
            }

            // 更新用户基本信息
            $result = $this->service->update($userData);
            if (!$result) {
                throw new \Exception('更新用户信息失败');
            }

            // 处理角色关联
            if (isset($params['roleIds']) && is_array($params['roleIds'])) {
                $userRoleService = new SysUserRoleService();
                // 过滤掉-1的无效角色ID
                $validRoleIds = array_filter($params['roleIds'], function($roleId) {
                    return $roleId > 0;
                });

                if (!empty($validRoleIds)) {
                    $roleParams = [
                        'user_id' => $params['id'],
                        'role_ids' => $validRoleIds
                    ];
                    $userRoleService->updateRoles($roleParams);
                } else {
                    // 如果没有有效角色，删除所有角色关联
                    $userRoleService->deleteRolesByUserId(['user_id' => $params['id']]);
                }
            }

            // 处理部门关联
            if (isset($params['dept_id']) && $params['dept_id'] > 0) {
                $userDeptService = new SysUserDeptService();
                // 先删除原有部门关联
                $userDeptService->getModel()->where('user_id', $params['id'])->delete();
                // 添加新的部门关联
                $userDeptService->getModel()->insert([
                    'user_id' => $params['id'],
                    'dept_id' => $params['dept_id']
                ]);
            }

            // 处理岗位关联（如果需要的话）
            if (isset($params['postIds']) && is_array($params['postIds'])) {
                // 这里可以添加岗位关联的处理逻辑
                // 由于数据库中没有看到岗位相关的表，暂时跳过
            }

            // 提交事务
            $this->service->getModel()->commit();

            return ApiResponse::success([], '用户信息更新成功');

        } catch (\Exception $e) {
            // 回滚事务
            $this->service->getModel()->rollback();
            return ApiResponse::paramError('更新失败：' . $e->getMessage());
        }
    }
}