import koi from "@/utils/request";

// 接口地址
const API = {
  LIST_PAGE: "/dogadmin/sysConfig/listPage",
  GET_BY_ID: "/dogadmin/sysConfig/getById",
  ADD: "/dogadmin/sysConfig/add",
  UPDATE: "/dogadmin/sysConfig/update",
  DELETE_BY_ID: "/dogadmin/sysConfig/deleteById",
  BATCH_DELETE: "/dogadmin/sysConfig/batchDelete",
  UPDATE_STATUS: "/dogadmin/sysConfig/updateStatus",
  GET_MAIL_CONFIG: "/dogadmin/sysConfig/getMailConfig",
  GET_STORAGE_CONFIG: "/dogadmin/sysConfig/getStorageConfig",
  UPDATE_MAIL_CONFIG: "/dogadmin/sysConfig/updateMailConfig",
  UPDATE_STORAGE_CONFIG: "/dogadmin/sysConfig/updateStorageConfig"
};

// 多条件分页查询数据
export const listPage = (params: any) => {
  return koi.get(API.LIST_PAGE, params);
};

// 根据ID进行查询
export const getById = (id: any) => {
  return koi.get(API.GET_BY_ID + "?id=" + id);
};

// 新增数据
export const add = (data: any) => {
  return koi.post(API.ADD, data);
};

// 根据ID进行修改
export const update = (data: any) => {
  return koi.post(API.UPDATE, data);
};

// 根据ID进行删除
export const deleteById = (id: any) => {
  return koi.post(API.DELETE_BY_ID, { id });
};

// 批量删除
export const batchDelete = (ids: any) => {
  return koi.post(API.BATCH_DELETE, { ids });
};

// 更新状态
export const updateStatus = (id: any, status: any) => {
  return koi.post(API.UPDATE_STATUS, { id, status });
};

// 获取邮件配置
export const getMailConfig = () => {
  return koi.get(API.GET_MAIL_CONFIG);
};

// 获取存储配置
export const getStorageConfig = () => {
  return koi.get(API.GET_STORAGE_CONFIG);
};

// 更新邮件配置
export const updateMailConfig = (data: any) => {
  return koi.post(API.UPDATE_MAIL_CONFIG, data);
};

// 更新存储配置
export const updateStorageConfig = (data: any) => {
  return koi.post(API.UPDATE_STORAGE_CONFIG, data);
};
