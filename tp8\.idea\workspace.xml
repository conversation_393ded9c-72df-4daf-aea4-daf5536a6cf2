<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db333b16-bba1-4f83-86ff-f9d8c45a2252" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/app/command/ProcessAsyncTasks.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/common_api/controller/TestEventController.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/lib/service/ai/AiServiceFactory.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/lib/service/ai/AiServiceInterface.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/lib/service/ai/GlmAiService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/lib/service/ai/使用文档.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/controller/RobotAiCharacter.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/controller/RobotAiCharacterDict.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/model/RobotAiCharacterDictModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/model/RobotAiCharacterModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/route/robot.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/service/RobotAiCharacterDictService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/service/RobotAiCharacterService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/sql/RobotAiCharacterDict_menu.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot/sql/RobotAiCharacter_menu.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/AsyncTaskProcessor.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/Base.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/RobotContact.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/RobotUserMoment.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/RobotUserMomentComment.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/controller/RobotUserMomentTask.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/AI的开发需求.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/全屏联系人选择器说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/删除联系人功能说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/发布页面集成和防重复点击说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/底部悬浮按钮设计说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/机器人评论等待组件说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/联系人功能开发完成说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/联系人开发需求.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/联系人选择功能说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/详情页面简化说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/doc/随机机器人排除逻辑说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/listener/UserRegisteredListener.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/BaseModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/RobotAiCharacterModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/RobotUserContactModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/RobotUserMomentCommentModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/RobotUserMomentModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/model/RobotUserMomentTaskModel.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/route/api.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/AsyncTaskProcessorService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/BaseService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/RobotContactService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/RobotUserMomentCommentService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/RobotUserMomentService.php" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/robot_api/service/RobotUserMomentTaskService.php" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution>
      <executable path="composer" />
    </execution>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\phpstudy_pro\Extensions\php\php8.0.2nts\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/topthink/think-filesystem" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-multi-app" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-trace" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-orm" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/thans/tp-jwt-auth" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/stella-maris/clock" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2oGwU4nDtm2aoLm2gMKHr1CFQmL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/work_code/universal-system/tp8&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\work_code\universal-system\tp8" />
      <recent name="E:\work_code\universal-system\tp8\config" />
      <recent name="E:\work_code\universal-system\tp8\app" />
      <recent name="E:\work_code\universal-system\tp8\app\lib" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\work_code\universal-system\tp8\app\common" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-PS-242.23339.16" />
        <option value="bundled-php-predefined-a98d8de5180a-90914f2295cb-com.jetbrains.php.sharedIndexes-PS-242.23339.16" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="db333b16-bba1-4f83-86ff-f9d8c45a2252" name="更改" comment="" />
      <created>1730509233125</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1730509233125</updated>
      <workItem from="1730509234163" duration="3530000" />
      <workItem from="1730529616814" duration="5796000" />
      <workItem from="1730596025669" duration="1477000" />
      <workItem from="1730623480366" duration="1253000" />
      <workItem from="1730682234537" duration="5156000" />
      <workItem from="1730702056077" duration="3604000" />
      <workItem from="1730705733727" duration="6505000" />
      <workItem from="1730766305650" duration="7448000" />
      <workItem from="1730787985404" duration="8559000" />
      <workItem from="1730854169511" duration="3818000" />
      <workItem from="1730874444296" duration="4148000" />
      <workItem from="1730939415916" duration="9077000" />
      <workItem from="1731122586990" duration="1543000" />
      <workItem from="1731285899744" duration="13568000" />
      <workItem from="1731460836528" duration="13000" />
      <workItem from="1731486508977" duration="2264000" />
      <workItem from="1732955277283" duration="41000" />
      <workItem from="1733100699109" duration="945000" />
      <workItem from="1733187399227" duration="3620000" />
      <workItem from="1733273140904" duration="13287000" />
      <workItem from="1733361360575" duration="2950000" />
      <workItem from="1733468704959" duration="5057000" />
      <workItem from="1733622617187" duration="12892000" />
      <workItem from="1733726976222" duration="4316000" />
      <workItem from="1733796180429" duration="615000" />
      <workItem from="1733963934554" duration="1665000" />
      <workItem from="1734162690108" duration="464000" />
      <workItem from="1749124699354" duration="610000" />
      <workItem from="1749169618440" duration="21000" />
      <workItem from="1749346925363" duration="610000" />
      <workItem from="1756363000003" duration="2249000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/app/common/service/admin/MenuService.php</url>
          <line>64</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/app/blog/route/admin.php</url>
          <line>31</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>