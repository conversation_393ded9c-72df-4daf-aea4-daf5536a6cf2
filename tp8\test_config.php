<?php
/**
 * 配置系统测试脚本
 * 用于测试数据库配置系统是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use app\dogadmin\common\ConfigManager;
use app\dogadmin\service\SysConfigService;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

echo "=== 配置系统测试 ===\n\n";

try {
    // 测试获取邮件配置
    echo "1. 测试获取邮件配置:\n";
    $mailConfig = ConfigManager::getMailConfig();
    print_r($mailConfig);
    echo "\n";
    
    // 测试获取存储配置
    echo "2. 测试获取存储配置:\n";
    $storageConfig = ConfigManager::getStorageConfig();
    print_r($storageConfig);
    echo "\n";
    
    // 测试获取单个配置
    echo "3. 测试获取单个配置:\n";
    $smtpHost = ConfigManager::get('mail.host', 'default_host');
    echo "SMTP Host: " . $smtpHost . "\n\n";
    
    // 测试配置服务
    echo "4. 测试配置服务:\n";
    $configService = new SysConfigService();
    
    // 测试更新配置
    $result = $configService->updateConfig('mail.host', 'test.smtp.com');
    echo "更新配置结果: " . ($result ? '成功' : '失败') . "\n";
    
    // 验证配置是否更新
    ConfigManager::clearCache(); // 清除缓存
    $newHost = ConfigManager::get('mail.host');
    echo "更新后的SMTP Host: " . $newHost . "\n\n";
    
    // 恢复原配置
    $configService->updateConfig('mail.host', 'smtp.163.com');
    echo "配置已恢复\n\n";
    
    echo "=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
