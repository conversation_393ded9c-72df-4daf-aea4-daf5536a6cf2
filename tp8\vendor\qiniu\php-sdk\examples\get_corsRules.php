<?php
require_once __DIR__ . '/../autoload.php';

use <PERSON><PERSON>\Auth;
use <PERSON><PERSON>\Config;
use <PERSON><PERSON>\Storage\BucketManager;

// 控制台获取密钥：https://portal.qiniu.com/user/key
$accessKey = getenv('QINIU_ACCESS_KEY');
$secretKey = getenv('QINIU_SECRET_KEY');

$auth = new Auth($accessKey, $secretKey);
$config = new Config();
$bucketManager = new BucketManager($auth, $config);

// 获取 bucket 设置的跨域信息
// 参考文档：https://developer.qiniu.com/kodo/manual/6094/set-cors

$bucket = 'xxxx'; // 存储空间名称

list($ret, $err) = $bucketManager->getCorsRules($bucket);
if ($err != null) {
    var_dump($err);
} else {
    var_dump($ret);
}
