<?php

namespace app\common\controller;

use app\common\service\CommonUploadService;
use app\dogadmin\common\ApiResponse;
use app\dogadmin\controller\Base;

/**
 * 用户上传管理 控制器
 */
class CommonUpload extends Base
{
    /**
     * @var CommonUploadService
     */
    protected $service;
    
    /**
     * 初始化方法
     * @return void
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = new CommonUploadService();
        $this->params = $this->request->param();
        $this->searchKey = [
            ['user_id' => 'like'],
        ];
    }
    
    // 所有基础CRUD方法均继承自Base控制器
     // 如需自定义方法，请在此处添加
}