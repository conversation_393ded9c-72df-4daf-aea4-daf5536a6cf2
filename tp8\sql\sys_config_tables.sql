-- 系统配置表
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `config_key` varchar(100) NOT NULL DEFAULT '' COMMENT '配置键名',
  `config_value` text COMMENT '配置键值',
  `config_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '配置类型(text:文本,number:数字,boolean:布尔,json:JSON)',
  `config_group` varchar(50) NOT NULL DEFAULT 'system' COMMENT '配置分组(system:系统,mail:邮件,storage:存储)',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `status` char(1) NOT NULL DEFAULT '1' COMMENT '状态(0:停用,1:启用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 邮件配置数据
INSERT INTO `sys_config` VALUES 
(1, 'SMTP服务器', 'mail.host', 'smtp.163.com', 'text', 'mail', 'SMTP服务器地址', '1', NOW(), NOW(), NULL),
(2, 'SMTP端口', 'mail.port', '465', 'number', 'mail', 'SMTP服务器端口', '1', NOW(), NOW(), NULL),
(3, '邮箱用户名', 'mail.username', '<EMAIL>', 'text', 'mail', '发送邮件的邮箱账号', '1', NOW(), NOW(), NULL),
(4, '邮箱密码', 'mail.password', 'WHBGTPDKCBCGCPAN', 'text', 'mail', '邮箱授权码或密码', '1', NOW(), NOW(), NULL),
(5, '发件人邮箱', 'mail.from', '<EMAIL>', 'text', 'mail', '发件人邮箱地址', '1', NOW(), NOW(), NULL),
(6, '发件人名称', 'mail.fromName', '来自注册的邮件', 'text', 'mail', '发件人显示名称', '1', NOW(), NOW(), NULL),
(7, '邮件模板目录', 'mail.template_dir', 'path/to/email/templates', 'text', 'mail', '邮件模板存放目录', '1', NOW(), NOW(), NULL);

-- 云存储配置数据
INSERT INTO `sys_config` VALUES 
(10, '默认存储类型', 'storage.default', 'qiniu', 'text', 'storage', '默认使用的存储类型', '1', NOW(), NOW(), NULL),
(11, '七牛云AccessKey', 'storage.qiniu.accessKey', '', 'text', 'storage', '七牛云访问密钥', '1', NOW(), NOW(), NULL),
(12, '七牛云SecretKey', 'storage.qiniu.secretKey', '', 'text', 'storage', '七牛云私钥', '1', NOW(), NOW(), NULL),
(13, '七牛云存储空间', 'storage.qiniu.bucket', '', 'text', 'storage', '七牛云存储空间名称', '1', NOW(), NOW(), NULL),
(14, '七牛云访问域名', 'storage.qiniu.domain', '', 'text', 'storage', '七牛云访问域名', '1', NOW(), NOW(), NULL),
(15, '七牛云最大文件大小', 'storage.qiniu.rules.maxSize', '10485760', 'number', 'storage', '七牛云最大文件大小(字节)', '1', NOW(), NOW(), NULL),
(16, '七牛云允许文件类型', 'storage.qiniu.rules.allowedTypes', '["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","mp3","mp4","avi","mov","zip","rar","json"]', 'json', 'storage', '七牛云允许的文件类型', '1', NOW(), NOW(), NULL),
(17, '七牛云小图后缀', 'storage.qiniu.suffix', '-small', 'text', 'storage', '七牛云小图后缀', '1', NOW(), NOW(), NULL),
(18, '七牛云迷你图后缀', 'storage.qiniu.mini_img_suffix', '-mini', 'text', 'storage', '七牛云迷你图后缀', '1', NOW(), NOW(), NULL),
(19, '七牛云普通图后缀', 'storage.qiniu.normal_img_suffix', '-normal', 'text', 'storage', '七牛云普通图后缀', '1', NOW(), NOW(), NULL),
(20, '腾讯云SecretId', 'storage.tencent_cos.secretId', '', 'text', 'storage', '腾讯云COS密钥ID', '1', NOW(), NOW(), NULL),
(21, '腾讯云SecretKey', 'storage.tencent_cos.secretKey', '', 'text', 'storage', '腾讯云COS私钥', '1', NOW(), NOW(), NULL),
(22, '腾讯云存储桶', 'storage.tencent_cos.bucket', '', 'text', 'storage', '腾讯云COS存储桶', '1', NOW(), NOW(), NULL),
(23, '腾讯云地域', 'storage.tencent_cos.region', 'ap-beijing', 'text', 'storage', '腾讯云COS地域', '1', NOW(), NOW(), NULL),
(24, '腾讯云域名', 'storage.tencent_cos.domain', '', 'text', 'storage', '腾讯云COS访问域名', '1', NOW(), NOW(), NULL),
(25, '腾讯云最大文件大小', 'storage.tencent_cos.rules.maxSize', '10485760', 'number', 'storage', '腾讯云COS最大文件大小(字节)', '1', NOW(), NOW(), NULL),
(26, '腾讯云允许文件类型', 'storage.tencent_cos.rules.allowedTypes', '["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","mp3","mp4","avi","mov","zip","rar","json"]', 'json', 'storage', '腾讯云COS允许的文件类型', '1', NOW(), NOW(), NULL),
(27, '阿里云AccessKeyId', 'storage.aliyun_oss.accessKeyId', '', 'text', 'storage', '阿里云OSS访问密钥ID', '1', NOW(), NOW(), NULL),
(28, '阿里云AccessKeySecret', 'storage.aliyun_oss.accessKeySecret', '', 'text', 'storage', '阿里云OSS访问密钥', '1', NOW(), NOW(), NULL),
(29, '阿里云存储桶', 'storage.aliyun_oss.bucket', '', 'text', 'storage', '阿里云OSS存储桶', '1', NOW(), NOW(), NULL),
(30, '阿里云节点', 'storage.aliyun_oss.endpoint', 'oss-cn-hangzhou.aliyuncs.com', 'text', 'storage', '阿里云OSS节点', '1', NOW(), NOW(), NULL),
(31, '阿里云域名', 'storage.aliyun_oss.domain', '', 'text', 'storage', '阿里云OSS访问域名', '1', NOW(), NOW(), NULL),
(32, '阿里云最大文件大小', 'storage.aliyun_oss.rules.maxSize', '10485760', 'number', 'storage', '阿里云OSS最大文件大小(字节)', '1', NOW(), NOW(), NULL),
(33, '阿里云允许文件类型', 'storage.aliyun_oss.rules.allowedTypes', '["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","mp3","mp4","avi","mov","zip","rar","json"]', 'json', 'storage', '阿里云OSS允许的文件类型', '1', NOW(), NOW(), NULL);

-- 上传规则配置
INSERT INTO `sys_config` VALUES 
(40, '图片最大大小', 'upload_rules.image.maxSize', '10485760', 'number', 'storage', '图片文件最大大小(字节)', '1', NOW(), NOW(), NULL),
(41, '图片允许类型', 'upload_rules.image.allowedTypes', '["jpg","jpeg","png","gif","bmp","webp"]', 'json', 'storage', '图片允许的文件类型', '1', NOW(), NOW(), NULL),
(42, '文档最大大小', 'upload_rules.document.maxSize', '20971520', 'number', 'storage', '文档文件最大大小(字节)', '1', NOW(), NOW(), NULL),
(43, '文档允许类型', 'upload_rules.document.allowedTypes', '["pdf","doc","docx","xls","xlsx","ppt","pptx","txt","json"]', 'json', 'storage', '文档允许的文件类型', '1', NOW(), NOW(), NULL),
(44, '媒体最大大小', 'upload_rules.media.maxSize', '104857600', 'number', 'storage', '媒体文件最大大小(字节)', '1', NOW(), NOW(), NULL),
(45, '媒体允许类型', 'upload_rules.media.allowedTypes', '["mp3","wav","flac","aac","ogg","mp4","avi","mov","wmv","flv","mkv"]', 'json', 'storage', '媒体允许的文件类型', '1', NOW(), NOW(), NULL),
(46, '压缩包最大大小', 'upload_rules.archive.maxSize', '52428800', 'number', 'storage', '压缩包文件最大大小(字节)', '1', NOW(), NOW(), NULL),
(47, '压缩包允许类型', 'upload_rules.archive.allowedTypes', '["zip","rar","7z","tar","gz"]', 'json', 'storage', '压缩包允许的文件类型', '1', NOW(), NOW(), NULL);

-- 上传路径配置
INSERT INTO `sys_config` VALUES 
(50, '图片上传路径', 'upload_paths.image', 'images/', 'text', 'storage', '图片文件上传路径', '1', NOW(), NOW(), NULL),
(51, '文档上传路径', 'upload_paths.document', 'documents/', 'text', 'storage', '文档文件上传路径', '1', NOW(), NOW(), NULL),
(52, '媒体上传路径', 'upload_paths.media', 'media/', 'text', 'storage', '媒体文件上传路径', '1', NOW(), NOW(), NULL),
(53, '压缩包上传路径', 'upload_paths.archive', 'archives/', 'text', 'storage', '压缩包文件上传路径', '1', NOW(), NOW(), NULL),
(54, '头像上传路径', 'upload_paths.avatar', 'avatars/', 'text', 'storage', '头像文件上传路径', '1', NOW(), NOW(), NULL),
(55, '临时文件路径', 'upload_paths.temp', 'temp/', 'text', 'storage', '临时文件上传路径', '1', NOW(), NOW(), NULL);
