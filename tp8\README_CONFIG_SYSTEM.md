# 数据库配置系统

## 项目概述

将原来的静态配置文件（mail.php、cloud_storage.php）迁移到数据库配置系统，实现动态配置管理。

## 文件结构

### 后端文件

```
tp8/
├── sql/
│   └── sys_config_tables.sql          # 数据库表创建和初始数据
├── app/dogadmin/
│   ├── model/
│   │   └── SysConfigModel.php         # 配置模型
│   ├── service/
│   │   └── SysConfigService.php       # 配置服务层
│   ├── controller/
│   │   └── SysConfig.php              # 配置控制器
│   ├── common/
│   │   └── ConfigManager.php          # 配置管理器
│   ├── command/
│   │   └── ConfigMigrate.php          # 配置迁移命令
│   └── route/
│       └── admin.php                  # 路由配置（已更新）
├── config/
│   ├── mail.php                       # 邮件配置（已修改为数据库读取）
│   └── cloud_storage.php              # 存储配置（已修改为数据库读取）
├── docs/
│   └── config_migration_guide.md      # 迁移指南
└── test_config.php                    # 配置系统测试脚本
```

### 前端文件

```
admin-web/src/
├── api/system/config/
│   └── index.ts                       # 配置管理API
└── views/system/config/
    └── index.vue                      # 配置管理页面
```

## 核心功能

### 1. 数据库配置存储
- 统一的sys_config表存储所有配置
- 支持多种配置类型：text、number、boolean、json
- 配置分组管理：system、mail、storage

### 2. 配置管理器 (ConfigManager)
- 提供统一的配置读取接口
- 内置缓存机制，提高性能
- 支持配置热更新

### 3. MVC架构
- **Model**: SysConfigModel - 数据库操作
- **Service**: SysConfigService - 业务逻辑
- **Controller**: SysConfig - API接口

### 4. 前端管理界面
- 配置列表查看和搜索
- 配置的增删改查
- 配置状态管理
- 专门的邮件和存储配置管理

## 使用方法

### 1. 数据库初始化

```bash
# 执行SQL脚本创建表和初始数据
mysql -u username -p database_name < tp8/sql/sys_config_tables.sql
```

### 2. 在代码中使用配置

```php
use app\dogadmin\common\ConfigManager;

// 获取邮件配置
$mailConfig = ConfigManager::getMailConfig();

// 获取存储配置
$storageConfig = ConfigManager::getStorageConfig();

// 获取单个配置值
$smtpHost = ConfigManager::get('mail.host', 'default_value');
```

### 3. 更新配置

```php
use app\dogadmin\service\SysConfigService;

$configService = new SysConfigService();

// 更新单个配置
$configService->updateConfig('mail.host', 'new_smtp_host');

// 批量更新配置
$configs = [
    'mail.host' => 'smtp.qq.com',
    'mail.port' => 587
];
$configService->updateConfigs($configs);
```

## API 接口

### 基础CRUD接口
- `GET /dogadmin/sysConfig/listPage` - 分页获取配置列表
- `GET /dogadmin/sysConfig/getById?id=1` - 获取单个配置
- `POST /dogadmin/sysConfig/add` - 新增配置
- `POST /dogadmin/sysConfig/update` - 更新配置
- `POST /dogadmin/sysConfig/deleteById` - 删除配置
- `POST /dogadmin/sysConfig/updateStatus` - 更新配置状态

### 专用配置接口
- `GET /dogadmin/sysConfig/getMailConfig` - 获取邮件配置
- `GET /dogadmin/sysConfig/getStorageConfig` - 获取存储配置
- `POST /dogadmin/sysConfig/updateMailConfig` - 更新邮件配置
- `POST /dogadmin/sysConfig/updateStorageConfig` - 更新存储配置

## 配置键名规范

### 邮件配置
- `mail.host` - SMTP服务器
- `mail.port` - SMTP端口
- `mail.username` - 邮箱用户名
- `mail.password` - 邮箱密码
- `mail.from` - 发件人邮箱
- `mail.fromName` - 发件人名称

### 存储配置
- `storage.default` - 默认存储类型
- `storage.qiniu.accessKey` - 七牛云AccessKey
- `storage.qiniu.secretKey` - 七牛云SecretKey
- `storage.tencent_cos.secretId` - 腾讯云SecretId
- `storage.aliyun_oss.accessKeyId` - 阿里云AccessKeyId

## 优势

1. **动态配置**: 无需重启应用即可更新配置
2. **统一管理**: 所有配置集中在数据库中管理
3. **权限控制**: 通过后台管理界面控制配置访问权限
4. **版本控制**: 配置变更有记录，便于追踪
5. **缓存优化**: 内置缓存机制，提高配置读取性能
6. **类型安全**: 支持多种配置类型，自动类型转换

## 注意事项

1. 原配置文件保留作为备用，数据库读取失败时使用
2. 配置更新后会自动清除缓存
3. 敏感配置建议加密存储
4. 生产环境建议定期备份sys_config表

## 测试

运行测试脚本验证配置系统：

```bash
cd tp8
php test_config.php
```
