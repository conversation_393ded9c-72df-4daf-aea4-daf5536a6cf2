<?php

namespace app\dogadmin\model;

class SysConfigModel extends BaseModel
{
    protected $table = 'sys_config';
    
    /**
     * 根据配置键获取配置值
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfigValue($key, $default = null)
    {
        $config = $this->where([
            ['config_key', '=', $key],
            ['status', '=', '1']
        ])->find();
        
        if (!$config) {
            return $default;
        }
        
        // 根据配置类型转换值
        switch ($config['config_type']) {
            case 'number':
                return (int)$config['config_value'];
            case 'boolean':
                return $config['config_value'] === '1' || $config['config_value'] === 'true';
            case 'json':
                return json_decode($config['config_value'], true) ?: $default;
            default:
                return $config['config_value'];
        }
    }
    
    /**
     * 批量获取配置
     * @param array $keys 配置键数组
     * @return array
     */
    public function getConfigs($keys = [])
    {
        $where = [['status', '=', '1']];
        if (!empty($keys)) {
            $where[] = ['config_key', 'in', $keys];
        }
        
        $configs = $this->where($where)->select()->toArray();
        $result = [];
        
        foreach ($configs as $config) {
            $value = $config['config_value'];
            
            // 根据配置类型转换值
            switch ($config['config_type']) {
                case 'number':
                    $value = (int)$value;
                    break;
                case 'boolean':
                    $value = $value === '1' || $value === 'true';
                    break;
                case 'json':
                    $value = json_decode($value, true) ?: [];
                    break;
            }
            
            $result[$config['config_key']] = $value;
        }
        
        return $result;
    }
    
    /**
     * 根据分组获取配置
     * @param string $group 配置分组
     * @return array
     */
    public function getConfigsByGroup($group)
    {
        $configs = $this->where([
            ['config_group', '=', $group],
            ['status', '=', '1']
        ])->select()->toArray();
        
        $result = [];
        foreach ($configs as $config) {
            $value = $config['config_value'];
            
            // 根据配置类型转换值
            switch ($config['config_type']) {
                case 'number':
                    $value = (int)$value;
                    break;
                case 'boolean':
                    $value = $value === '1' || $value === 'true';
                    break;
                case 'json':
                    $value = json_decode($value, true) ?: [];
                    break;
            }
            
            $result[$config['config_key']] = $value;
        }
        
        return $result;
    }
}
