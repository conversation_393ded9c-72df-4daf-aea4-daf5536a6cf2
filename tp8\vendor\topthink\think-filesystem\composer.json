{"name": "topthink/think-filesystem", "description": "The ThinkPHP6.1 Filesystem Package", "type": "library", "license": "Apache-2.0", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"think\\": "src"}}, "autoload-dev": {"psr-4": {"think\\tests\\": "tests/"}}, "require": {"php": ">=7.2.5", "topthink/framework": "^6.1|^8.0", "league/flysystem": "^1.1.4", "league/flysystem-cached-adapter": "^1.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^8.0"}, "minimum-stability": "dev", "prefer-stable": true}