/*
 Navicat Premium Data Transfer

 Source Server         : local
 Source Server Type    : MySQL
 Source Server Version : 50740 (5.7.40)
 Source Host           : localhost:3306
 Source Schema         : dogadmin

 Target Server Type    : MySQL
 Target Server Version : 50740 (5.7.40)
 File Encoding         : 65001

 Date: 28/08/2025 15:59:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for common_upload
-- ----------------------------
DROP TABLE IF EXISTS `common_upload`;
CREATE TABLE `common_upload`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始文件名',
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件存储路径/云存储key',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件访问URL',
  `file_size` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
  `file_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件类型/扩展名',
  `upload_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'common' COMMENT '上传类型(common,avatar,moment,etc)',
  `sys_file_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '关联sys_file表ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:正常,0:删除)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通用上传文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of common_upload
-- ----------------------------

-- ----------------------------
-- Table structure for common_user
-- ----------------------------
DROP TABLE IF EXISTS `common_user`;
CREATE TABLE `common_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `union_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `openid_mp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `openid_qq` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `avatar_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `coin` int(11) NULL DEFAULT 0,
  `money` decimal(10, 2) NULL DEFAULT 0.00,
  `gender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1',
  `platform` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `game_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `delete_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of common_user
-- ----------------------------

-- ----------------------------
-- Table structure for common_user_black
-- ----------------------------
DROP TABLE IF EXISTS `common_user_black`;
CREATE TABLE `common_user_black`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `delete_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of common_user_black
-- ----------------------------

-- ----------------------------
-- Table structure for sys_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `sys_blacklist`;
CREATE TABLE `sys_blacklist`  (
  `id` int(20) NOT NULL COMMENT '主键ID',
  `black_ip` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `black_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名单类型[1-黑名单 2-白名单]',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '是否拉黑[0否- 1-是]',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '黑名单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_blacklist
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` int(20) NOT NULL DEFAULT 0 COMMENT '父部门ID',
  `dept_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态(1正常 0停用)',
  `sorted` int(11) NULL DEFAULT 1,
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `dict_label` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态[0停用 1启用]',
  `dict_tag` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'primary' COMMENT 'ElementPlus官方颜色[默认-primary]',
  `dict_color` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'tags自定义背景颜色[有值会进行覆盖ElementPlus官方颜色](例如：16进制或者pink等)',
  `sorted` int(11) NULL DEFAULT 0 COMMENT '显示顺序',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 'sys_switch_status', '启用', '1', '1', 'primary', '', 2, '正常状态', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (2, 'sys_switch_status', '停用', '0', '1', 'danger', '', 1, '停用状态', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (3, 'sys_user_sex', '男', '1', '1', 'primary', '', 1, '性别男', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (4, 'sys_user_sex', '女', '2', '1', 'danger', '', 2, '性别女', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (5, 'sys_user_sex', '未知', '3', '1', 'info', '', 3, '性别男', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (6, 'sys_tag_type', 'primary', '1', '1', 'primary', '', 1, 'el-tag的primary类型', '2023-12-30 12:00:00', '2024-02-06 17:30:25', NULL);
INSERT INTO `sys_dict_data` VALUES (7, 'sys_tag_type', 'warning', '2', '1', 'warning', '', 2, 'el-tag的warning类型', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (8, 'sys_tag_type', 'success', '3', '1', 'success', '', 3, 'el-tag的success类型', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (9, 'sys_tag_type', 'danger', '4', '1', 'danger', '', 4, 'el-tag的danger类型', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (10, 'sys_tag_type', 'info', '5', '1', 'info', '', 5, 'el-tag的info类型', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (11, 'sys_notice_type', '通知', '1', '1', 'primary', '', 1, '消息通知', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (12, 'sys_notice_type', '公告', '2', '1', 'warning', '', 2, '消息公告', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (13, 'sys_user_type', '系统用户', '1', '1', 'primary', '', 1, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (14, 'sys_user_type', '注册用户', '2', '1', 'warning', '', 2, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (15, 'sys_user_type', '微信用户', '3', '1', 'success', '', 3, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (16, 'sys_menu_type', '目录', '1', '1', 'primary', '', 1, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (17, 'sys_menu_type', '菜单', '2', '1', 'warning', '', 2, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (18, 'sys_menu_type', '按钮', '3', '1', 'success', '', 3, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (19, 'sys_oper_type', '其他', '0', '1', 'info', '', 99, '其他操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (20, 'sys_oper_type', '新增', '1', '1', 'info', '', 1, '新增操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (21, 'sys_oper_type', '修改', '2', '1', 'info', '', 2, '修改操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (22, 'sys_oper_type', '删除', '3', '1', 'danger', '', 3, '删除操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (23, 'sys_oper_type', '授权', '4', '1', 'primary', '', 4, '授权操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (24, 'sys_oper_type', '导出', '5', '1', 'warning', '', 5, '导出操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (25, 'sys_oper_type', '导入', '6', '1', 'warning', '', 6, '导入操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (26, 'sys_oper_type', '强退', '7', '1', 'danger', '', 7, '强退操作', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (27, 'sys_job_type', '管理平台', '1', '1', 'primary', '', 1, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (28, 'sys_job_type', '小程序', '2', '1', 'success', '', 2, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (29, 'sys_job_type', 'App', '3', '1', 'warning', '', 3, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (30, 'sys_policy_status', '立即执行', '1', '1', 'primary', '', 1, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (31, 'sys_policy_status', '执行一次', '2', '1', 'success', '', 2, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (32, 'sys_policy_status', '放弃执行', '3', '1', 'danger', '', 3, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (33, 'sys_common_status', '初始化', '0', '1', 'primary', '', 1, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (34, 'sys_common_status', '成功', '1', '1', 'success', '', 2, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (35, 'sys_common_status', '失败', '2', '1', 'danger', '', 3, '', '2023-12-30 12:00:00', '2023-12-30 12:00:00', NULL);
INSERT INTO `sys_dict_data` VALUES (36, 'sys_yes_no', '是', '0', '1', 'primary', '', 1, '', '2024-08-23 11:09:56', '2024-08-23 11:09:56', NULL);
INSERT INTO `sys_dict_data` VALUES (37, 'sys_yes_no', '否', '1', '1', 'danger', '', 2, '', '2024-08-23 11:10:07', '2024-08-30 11:50:48', NULL);
INSERT INTO `sys_dict_data` VALUES (38, 'sys_file_service', 'LOCAL', '1', '1', 'primary', '', 1, '', '2024-09-12 20:32:35', '2024-09-12 20:56:14', NULL);
INSERT INTO `sys_dict_data` VALUES (39, 'sys_file_service', 'QINIU', '2', '1', 'info', '', 2, '七牛云', '2024-09-12 20:33:22', '2025-06-05 19:29:08', NULL);
INSERT INTO `sys_dict_data` VALUES (40, 'sys_file_service', 'OSS', '3', '1', 'warning', '', 3, '', '2024-09-12 20:32:58', '2024-09-12 20:46:11', NULL);
INSERT INTO `sys_dict_data` VALUES (46, 'sys_file_type', '✨️ 全部', '0', '1', 'primary', '', 0, '', '2024-09-13 15:14:15', '2024-09-13 16:49:41', NULL);
INSERT INTO `sys_dict_data` VALUES (47, 'sys_file_type', '🎀 图片', '1', '1', 'primary', '', 1, '', '2024-09-13 11:48:18', '2024-09-13 16:49:20', NULL);
INSERT INTO `sys_dict_data` VALUES (48, 'sys_file_type', '📂 文档', '2', '1', 'warning', '', 2, '', '2024-09-13 11:48:34', '2024-09-13 16:49:47', NULL);
INSERT INTO `sys_dict_data` VALUES (49, 'sys_file_type', '🎵 音频', '3', '1', 'success', '', 3, '', '2024-09-13 11:48:59', '2024-09-13 16:50:52', NULL);
INSERT INTO `sys_dict_data` VALUES (50, 'sys_file_type', '📺️ 视频', '4', '1', 'info', '', 4, '', '2024-09-13 11:49:15', '2024-09-13 16:51:12', NULL);
INSERT INTO `sys_dict_data` VALUES (51, 'sys_file_type', '🎁 压缩包', '5', '1', 'danger', '', 5, '', '2024-09-13 11:49:30', '2024-09-13 16:51:48', NULL);
INSERT INTO `sys_dict_data` VALUES (52, 'sys_file_type', '🦄 应用程序', '6', '1', 'danger', '', 6, '', '2024-09-13 11:49:44', '2024-09-13 16:52:09', NULL);
INSERT INTO `sys_dict_data` VALUES (53, 'sys_file_type', '🎃 其他', '9', '1', 'info', '', 7, '', '2024-09-13 11:47:40', '2024-09-13 16:52:59', NULL);
INSERT INTO `sys_dict_data` VALUES (61, 'sys_picture_type', '全部数据', '0', '1', 'primary', '', 1, '', '2024-09-18 16:52:01', '2024-09-18 17:45:01', NULL);
INSERT INTO `sys_dict_data` VALUES (62, 'sys_picture_type', '用户头像', '1', '1', 'info', '', 2, '', '2024-09-18 16:56:50', '2024-09-18 16:56:50', NULL);
INSERT INTO `sys_dict_data` VALUES (63, 'sys_picture_type', '动漫分类', '2', '1', 'warning', '', 3, '', '2024-09-18 16:57:13', '2024-09-18 16:57:13', NULL);
INSERT INTO `sys_dict_data` VALUES (64, 'sys_picture_type', '美女分类', '3', '1', 'danger', '', 4, '', '2024-09-18 16:57:29', '2024-09-18 16:57:29', NULL);
INSERT INTO `sys_dict_data` VALUES (65, 'sys_picture_type', '风景分类', '4', '1', 'success', '', 5, '', '2024-09-18 16:57:41', '2024-09-18 16:57:41', NULL);
INSERT INTO `sys_dict_data` VALUES (66, 'sys_picture_type', '明星分类', '5', '1', 'primary', '', 6, '', '2024-09-18 16:57:59', '2024-09-18 16:57:59', NULL);
INSERT INTO `sys_dict_data` VALUES (67, 'sys_picture_type', '动物分类', '6', '1', 'warning', '', 7, '', '2024-09-18 16:58:11', '2024-09-18 16:58:44', NULL);
INSERT INTO `sys_dict_data` VALUES (68, 'sys_picture_type', '其他分类', '9', '1', 'info', '', 8, '', '2024-09-18 16:58:35', '2024-09-18 16:58:35', NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态[0-停用 1-正常]',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '系统开关', 'sys_switch_status', '1', '系统开关列表', '2024-01-08 17:19:43', '2025-05-30 20:20:57', NULL);
INSERT INTO `sys_dict_type` VALUES (2, '用户性别', 'sys_user_sex', '1', '用户性别列表', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (3, '用户类型', 'sys_user_type', '1', '用户类型[1-系统用户，2-注册用户，3-微信小程序用户]', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (4, 'Tags标签', 'sys_tag_type', '1', 'ElementPlus默认类型，warning等', '2024-01-08 17:19:43', '2024-12-04 16:40:53', NULL);
INSERT INTO `sys_dict_type` VALUES (5, '菜单类型', 'sys_menu_type', '1', '菜单类型列表', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (6, '通知公告', 'sys_notice_type', '1', '通知公告类型', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (7, '任务类型', 'sys_job_type', '1', '定时任务类型[1-管理平台 2-小程序 3-App]', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (8, 'cron计划策略', 'sys_policy_status', '1', 'cron计划策略[1-立即触发执行,2-触发一次执行,3-放弃执行]', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (9, '系统状态', 'sys_common_status', '1', '系统公用状态[0-初始化 1-成功 1-失败]', '2024-01-08 17:19:43', '2024-01-08 17:21:19', NULL);
INSERT INTO `sys_dict_type` VALUES (10, '系统是否', 'sys_yes_no', '1', '系统是否[0-是 1-否]', '2024-08-23 11:09:37', '2024-08-23 11:09:37', NULL);
INSERT INTO `sys_dict_type` VALUES (11, '文件服务', 'sys_file_service', '1', '文件服务类型', '2024-09-12 20:32:15', '2024-09-12 20:32:15', NULL);
INSERT INTO `sys_dict_type` VALUES (12, '文件类型', 'sys_file_type', '1', '文件类型', '2024-09-13 11:46:58', '2024-09-13 11:47:08', NULL);
INSERT INTO `sys_dict_type` VALUES (13, '图片类型', 'sys_picture_type', '1', '图片类型', '2024-09-18 16:49:48', '2024-09-18 16:49:59', NULL);
INSERT INTO `sys_dict_type` VALUES (21, '友链类型', 'blog_friend_type', '1', '友链类型', '2024-04-28 18:37:51', '2024-04-28 18:37:51', NULL);
INSERT INTO `sys_dict_type` VALUES (22, '文章类型', 'blog_article_type', '1', '文章类型[1-文章 2-知识库 3-两者都 4-密码]', '2024-08-20 14:18:48', '2024-08-20 14:18:48', NULL);
INSERT INTO `sys_dict_type` VALUES (23, '目录类型', 'blog_catalog_type', '1', '目录类型[1-目录 2-文章]', '2024-08-21 14:11:24', '2024-08-21 14:11:24', NULL);
INSERT INTO `sys_dict_type` VALUES (24, '攻略分类', 'guide_type', '1', '迷雾大陆攻略分类', '2024-12-13 15:29:47', '2024-12-13 15:31:02', NULL);

-- ----------------------------
-- Table structure for sys_file
-- ----------------------------
DROP TABLE IF EXISTS `sys_file`;
CREATE TABLE `sys_file`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件原始名称',
  `new_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件新名称',
  `file_mime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '文件类型[0-全部 1-图片 2-文档 3-音频 4-视频 5-压缩包 6-应用程序 9-其他]',
  `file_size` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件大小[KB/MB/GB]',
  `file_ext` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件后缀',
  `file_upload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件上传路径',
  `file_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件回显路径',
  `file_suffix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片后缀',
  `file_service` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '文件服务类型[1-LOCAL，2-qiniu]',
  `status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `delete_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件资源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_file
-- ----------------------------

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log`  (
  `id` int(20) NOT NULL COMMENT '访问ID',
  `login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录账号',
  `device_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录设备',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地址',
  `browser` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `login_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态[0-成功 1-失败]',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_login_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
  `en_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '英文菜单',
  `parent_id` int(20) NOT NULL COMMENT '父菜单ID',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '菜单类型[1-目录 2-菜单 3-按钮]',
  `path` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路由地址',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由名称[例如：userPage]',
  `component` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求地址',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'Aim' COMMENT '菜单图标',
  `auth` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '权限标识',
  `status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '是否启用[0-停用 1-启用]',
  `active_menu` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '选中路由',
  `is_hide` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '是否隐藏[0-隐藏 1-显示]',
  `is_link` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '是否外链[空值不跳转，有值则跳转链接]',
  `is_keep_alive` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否缓存[0-是 1-否]',
  `is_full` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '是否全屏[0-是 1-否]',
  `is_affix` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否固定项[0-是 1-否][例如：首页]',
  `is_spread` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否展开[0-是 1-否]',
  `sorted` int(11) NULL DEFAULT NULL COMMENT '显示顺序',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 397 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 'System Manage', 0, '1', '/system', 'systemPage', '', 'Tools', 'dogadmin:system:auth', '1', NULL, '1', '', '0', '1', '1', '1', 1, NULL, '2025-05-28 16:00:32', NULL);
INSERT INTO `sys_menu` VALUES (2, '用户管理', 'User Manage', 1, '2', '/system/user', 'userPage', 'system/user/index', 'UserFilled', 'dogadmin:sysUser:listPage', '1', NULL, '1', '', '0', '1', '1', '1', 1, NULL, '2024-07-04 20:54:36', NULL);
INSERT INTO `sys_menu` VALUES (3, '搜索', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:listPage', '1', NULL, '0', '', '0', '1', '1', '1', 1, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (4, '新增', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:add', '1', NULL, '0', '', '0', '1', '1', '1', 2, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (5, '修改', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:update', '1', NULL, '0', '', '0', '1', '1', '1', 3, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (6, '删除', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:delete', '1', NULL, '0', '', '0', '1', '1', '1', 4, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (7, '导出', '', 2, '3', '', NULL, NULL, '', 'dogadmin:SysUser:export', '1', NULL, '0', '', '0', '1', '1', '1', 5, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (8, '导入', '', 2, '3', '', NULL, NULL, '', 'dogadmin:SysUser:import', '1', NULL, '0', '', '0', '1', '1', '1', 6, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (9, '重置密码', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:resetPwd', '1', NULL, '0', '', '0', '1', '1', '1', 7, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (10, '分配角色', '', 2, '3', '', NULL, NULL, '', 'dogadmin:sysUser:role', '1', NULL, '0', '', '0', '1', '1', '1', 8, NULL, '2024-01-03 15:12:23', NULL);
INSERT INTO `sys_menu` VALUES (11, '分配岗位', '', 2, '3', '', '', '', '', 'dogadmin:SysUser:post', '1', NULL, '0', '', '0', '1', '1', '1', 9, '2024-01-03 15:12:12', '2024-01-03 15:12:12', NULL);
INSERT INTO `sys_menu` VALUES (12, '角色管理', 'Role Manage', 1, '2', '/system/role', 'rolePage', 'system/role/index', 'Avatar', 'dogadmin:sysRole:listPage', '1', NULL, '1', '', '0', '1', '1', '1', 2, NULL, '2023-09-07 15:19:35', NULL);
INSERT INTO `sys_menu` VALUES (13, '搜索', '', 12, '3', '', NULL, NULL, '', 'dogadmin:sysRole:listPage', '1', NULL, '0', '', '0', '1', '1', '1', 1, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (14, '新增', '', 12, '3', '', NULL, NULL, '', 'dogadmin:sysRole:add', '1', NULL, '0', '', '0', '1', '1', '1', 2, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (15, '修改', '', 12, '3', '', NULL, NULL, '', 'dogadmin:sysRole:update', '1', NULL, '0', '', '0', '1', '1', '1', 3, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (16, '删除', '', 12, '3', '', NULL, NULL, '', 'dogadmin:sysRole:delete', '1', NULL, '0', '', '0', '1', '1', '1', 4, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (17, '分配菜单', '', 12, '3', '', '', '', '', 'dogadmin:sysRole:menu', '1', NULL, '0', '', '0', '1', '1', '1', 5, '2023-12-29 14:34:30', '2023-12-29 14:34:30', NULL);
INSERT INTO `sys_menu` VALUES (18, '分配部门', '', 12, '3', '', '', '', '', 'dogadmin:sysRole:dept', '1', NULL, '0', '', '0', '1', '1', '1', 6, '2023-12-29 14:35:15', '2023-12-29 14:35:15', NULL);
INSERT INTO `sys_menu` VALUES (19, '菜单管理', 'Menu Manage', 1, '2', '/system/menu', 'menuPage', 'system/menu/index', 'Grid', 'dogadmin:sysMenu:listPage', '1', NULL, '1', '', '0', '1', '1', '1', 3, NULL, '2023-12-28 11:49:44', NULL);
INSERT INTO `sys_menu` VALUES (20, '搜索', '', 19, '3', '', NULL, NULL, '', 'dogadmin:sysMenu:listPage', '1', NULL, '0', '', '0', '1', '1', '1', 1, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (21, '新增', '', 19, '3', '', NULL, NULL, '', 'dogadmin:sysMenu:add', '1', NULL, '0', '', '0', '1', '1', '1', 2, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (22, '修改', '', 19, '3', '', NULL, NULL, '', 'dogadmin:sysMenu:update', '1', NULL, '0', '', '0', '1', '1', '1', 3, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (23, '删除', '', 19, '3', '', NULL, NULL, '', 'dogadmin:sysMenu:delete', '1', NULL, '0', '', '0', '1', '1', '1', 4, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (24, '字典管理', 'DictType Manage', 1, '2', '/system/dict/type', 'dictTypePage', 'system/dict/type', 'Management', 'dogadmin:sysDictType:listPage', '1', NULL, '1', '', '0', '1', '1', '1', 4, NULL, '2023-12-28 11:49:50', NULL);
INSERT INTO `sys_menu` VALUES (25, '搜索', '', 24, '3', '', '', '', '', 'dogadmin:sysDictType:listPage', '1', NULL, '0', '', '0', '1', '1', '1', 1, '2023-12-27 17:52:32', '2023-12-27 17:52:32', NULL);
INSERT INTO `sys_menu` VALUES (26, '新增', '', 24, '3', '', '', '', '', 'dogadmin:sysDictType:add', '1', NULL, '0', '', '0', '1', '1', '1', 2, '2023-12-27 19:30:44', '2023-12-27 19:31:10', NULL);
INSERT INTO `sys_menu` VALUES (27, '修改', '', 24, '3', '', '', '', '', 'dogadmin:sysDictType:update', '1', NULL, '0', '', '0', '1', '1', '1', 3, '2023-12-27 20:08:28', '2023-12-28 09:19:55', NULL);
INSERT INTO `sys_menu` VALUES (28, '删除', '', 24, '3', '', '', '', '', 'dogadmin:sysDictType:delete', '1', NULL, '0', '', '0', '1', '1', '1', 4, '2023-12-28 09:19:44', '2023-12-28 09:19:44', NULL);
INSERT INTO `sys_menu` VALUES (50, '个人中心', 'Personage Center', 1, '2', '/system/personage', 'personagePage', 'system/personage/index', 'User', 'dogadmin:sysUser:info', '1', NULL, '1', '', '0', '1', '1', '1', 10, '2023-12-28 17:11:37', '2023-12-28 17:15:43', NULL);
INSERT INTO `sys_menu` VALUES (70, '系统工具', 'System Tool', 0, '1', '/tools', 'toolsPage', '', 'FirstAidKit', 'dogadmin:tools:auth', '1', NULL, '1', '', '0', '1', '1', '1', 3, '2024-01-16 16:15:09', '2024-01-16 16:15:09', NULL);
INSERT INTO `sys_menu` VALUES (72, '文件管理', 'Files Manage', 70, '2', '/tools/file', 'filePage', 'system/file/index', 'FolderOpened', 'dogadmin:sysFile:listPage', '1', '', '1', '', '0', '1', '1', '1', 2, '2024-09-12 18:17:35', '2024-09-13 15:35:28', NULL);
INSERT INTO `sys_menu` VALUES (73, '搜索', '', 72, '3', '', '', '', '', 'dogadmin:sysFile:listPage', '1', '', '0', '', '0', '1', '1', '1', 1, '2024-09-14 13:21:46', '2024-09-14 13:21:46', NULL);
INSERT INTO `sys_menu` VALUES (74, '上传', '', 72, '3', '', '', '', '', 'dogadmin:sysFile:upload', '1', '', '0', '', '0', '1', '1', '1', 2, '2024-09-14 13:22:22', '2024-09-14 13:22:22', NULL);
INSERT INTO `sys_menu` VALUES (75, '下载', '', 72, '3', '', '', '', '', 'dogadmin:sysFile:download', '1', '', '0', '', '0', '1', '1', '1', 3, '2024-09-14 13:22:38', '2024-09-14 13:22:38', NULL);
INSERT INTO `sys_menu` VALUES (76, '删除', '', 72, '3', '', '', '', '', 'dogadmin:sysFile:delete', '1', '', '0', '', '0', '1', '1', '1', 4, '2024-09-14 13:22:51', '2024-09-14 13:22:51', NULL);
INSERT INTO `sys_menu` VALUES (77, '图库管理', 'Pictures Manage', 70, '2', '/tools/picture', 'picturePage', 'system/picture/index', 'Picture', 'dogadmin:sysPicture:listPage', '1', '', '1', '', '0', '1', '1', '1', 3, '2024-09-18 17:22:19', '2024-09-18 17:22:19', NULL);
INSERT INTO `sys_menu` VALUES (78, '搜索', '', 77, '3', '', '', '', '', 'dogadmin:sysPicture:listPage', '1', '', '0', '', '0', '1', '1', '1', 1, '2024-09-18 19:42:15', '2024-09-18 19:42:15', NULL);
INSERT INTO `sys_menu` VALUES (79, '上传', '', 77, '3', '', '', '', '', 'dogadmin:sysPicture:upload', '1', '', '0', '', '0', '1', '1', '1', 2, '2024-09-18 19:42:38', '2024-09-18 19:42:38', NULL);
INSERT INTO `sys_menu` VALUES (80, '下载', '', 77, '3', '', '', '', '', 'dogadmin:sysPicture:download', '1', '', '0', '', '0', '1', '1', '1', 3, '2024-09-18 19:42:55', '2024-09-18 19:42:55', NULL);
INSERT INTO `sys_menu` VALUES (81, '删除', '', 77, '3', '', '', '', '', 'dogadmin:sysPicture:delete', '1', '', '0', '', '0', '1', '1', '1', 4, '2024-09-18 19:43:13', '2024-09-18 19:43:13', NULL);
INSERT INTO `sys_menu` VALUES (89, '部门管理', 'SysDept', 1, '2', '/system/dept', 'SysDept', 'system/dept/index', 'Apple', 'dogadmin:sysDept:listPage', '1', '/system/dept/index', '1', '', '0', '1', '1', '1', 1, '2025-05-28 16:40:04', '2025-05-28 16:40:04', NULL);
INSERT INTO `sys_menu` VALUES (90, '角色下拉列表', '', 12, '3', '', '', '', 'Aim', 'dogadmin:sysRole:listRoleElSelect', '1', '', '0', '', '0', '1', '1', '1', 7, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (91, '获取路由列表', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:listRouters', '1', '', '0', '', '0', '1', '1', '1', 5, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (92, '获取菜单列表', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:listMenuNormal', '1', '', '0', '', '0', '1', '1', '1', 6, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (93, '获取角色菜单', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:listMenuIdsByRoleId', '1', '', '0', '', '0', '1', '1', '1', 7, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (94, '保存角色菜单', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:saveRoleMenu', '1', '', '0', '', '0', '1', '1', '1', 8, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (95, '级联菜单列表', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:cascaderList', '1', '', '0', '', '0', '1', '1', '1', 9, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (96, '添加按钮权限', '', 19, '3', '', '', '', 'Aim', 'dogadmin:sysMenu:addButtonAuth', '1', '', '0', '', '0', '1', '1', '1', 10, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (97, '级联部门列表', '', 89, '3', '', '', '', 'Aim', 'dogadmin:sysDept:cascaderList', '1', '', '0', '', '0', '1', '1', '1', 5, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (98, '获取角色部门', '', 89, '3', '', '', '', 'Aim', 'dogadmin:sysDept:listDeptIdsByRoleId', '1', '', '0', '', '0', '1', '1', '1', 6, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (99, '保存角色部门', '', 89, '3', '', '', '', 'Aim', 'dogadmin:sysDept:saveRoleDept', '1', '', '0', '', '0', '1', '1', '1', 7, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (100, '获取字典数据', '', 24, '3', '', '', '', 'Aim', 'dogadmin:sysDictData:getDictDataByType', '1', '', '0', '', '0', '1', '1', '1', 5, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (101, '获取个人信息', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:getUserInfo', '1', '', '0', '', '0', '1', '1', '1', 1, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (102, '获取个人资料', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:getPersonalData', '1', '', '0', '', '0', '1', '1', '1', 2, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (103, '更新个人资料', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:updateBasicData', '1', '', '0', '', '0', '1', '1', '1', 3, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (104, '修改密码', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:resetUserPwd', '1', '', '0', '', '0', '1', '1', '1', 4, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (105, '获取验证码', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:captcha', '1', '', '0', '', '0', '1', '1', '1', 5, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (106, '获取原始密码', '', 50, '3', '', '', '', 'Aim', 'dogadmin:sysUser:originPassword', '1', '', '0', '', '0', '1', '1', '1', 6, NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (107, '代码生成工具', 'Code Build Tool', 70, '2', '/system/build', 'buildPage', 'system/build/index', 'Document', 'dogadmin:sysBuild:listPage', '1', NULL, '1', '', '0', '1', '1', '1', 4, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (108, '获取表列表', '', 107, '3', '', '', '', '', 'dogadmin:sysBuild:listTables', '1', '', '0', '', '0', '1', '1', '1', 1, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (109, '获取表信息', '', 107, '3', '', '', '', '', 'dogadmin:sysBuild:getTableInfo', '1', '', '0', '', '0', '1', '1', '1', 2, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (110, '预览代码', '', 107, '3', '', '', '', '', 'dogadmin:sysBuild:preview', '1', '', '0', '', '0', '1', '1', '1', 3, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (111, '生成代码', '', 107, '3', '', '', '', '', 'dogadmin:sysBuild:generate', '1', '', '0', '', '0', '1', '1', '1', 4, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (112, '获取API文档', '', 107, '3', '', '', '', '', 'dogadmin:sysBuild:getApiDocs', '1', '', '0', '', '0', '1', '1', '1', 5, '2025-06-05 21:10:51', '2025-06-05 21:10:51', NULL);
INSERT INTO `sys_menu` VALUES (132, '通用管理', '', 0, '1', 'commonUser', '', '', 'FolderOpened', 'common:commonUser:listPage', '1', '', '1', '', '0', '1', '1', '1', 999, '2025-06-16 10:22:09', '2025-06-16 10:22:30', NULL);
INSERT INTO `sys_menu` VALUES (133, '用户管理', '', 132, '2', '/common/commonUser/index', 'commonUserPage', 'common/commonUser/index', 'Menu', 'common:commonUser:listPage', '1', '', '1', '', '0', '1', '1', '1', 999, '2025-06-16 10:22:09', '2025-06-16 10:22:46', NULL);
INSERT INTO `sys_menu` VALUES (134, '查询', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:listPage', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (135, '获取详情', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:getById', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (136, '获取排序', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:getSorted', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (137, '新增', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:add', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (138, '修改', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:update', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (139, '删除', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:deleteById', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (140, '批量删除', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:batchDelete', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (141, '更新状态', '', 133, '3', '', '', '', 'Aim', 'common:commonUser:updateStatus', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-06-16 10:22:09', '2025-06-16 10:22:09', NULL);
INSERT INTO `sys_menu` VALUES (388, '用户上传管理', '', 132, '2', '/common/commonUpload/index', 'commonUploadPage', 'common/commonUpload/index', 'Menu', 'common:commonUpload:listPage', '1', '', '1', '', '0', '1', '1', '1', 1, '2025-08-28 15:50:54', '2025-08-28 15:51:43', NULL);
INSERT INTO `sys_menu` VALUES (389, '查询', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:listPage', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (390, '获取详情', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:getById', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (391, '获取排序', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:getSorted', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (392, '新增', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:add', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (393, '修改', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:update', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (394, '删除', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:deleteById', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (395, '批量删除', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:batchDelete', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);
INSERT INTO `sys_menu` VALUES (396, '更新状态', '', 388, '3', '', '', '', 'Aim', 'common:commonUpload:updateStatus', '1', '', '0', '', '0', '1', '1', '1', NULL, '2025-08-28 15:50:54', '2025-08-28 15:50:54', NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `role_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色权限编码',
  `sorted` int(11) NOT NULL COMMENT '显示顺序',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '角色状态[0-停用 1-正常]',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `data_scope` int(1) NOT NULL DEFAULT 2 COMMENT '数据范围(1:全部数据权限 2:自定义数据权限 3:本部门数据权限 4:本部门及以下数据权限 5:仅本人数据权限)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, 'KOI-ADMIN', 'KOI_ADMIN', 1, '超级管理员1', '1', '2024-02-02 11:27:03', '2025-05-28 10:21:58', NULL, 1);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` int(20) NOT NULL COMMENT '角色ID',
  `dept_id` int(20) NOT NULL COMMENT '部门ID'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` int(20) NOT NULL COMMENT '角色ID',
  `menu_id` int(20) NOT NULL COMMENT '菜单ID'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `login_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录账号',
  `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名字',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '用户性别[1-男 2-女 3-未知]',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像路径',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP',
  `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `pwd_update_time` datetime NULL DEFAULT NULL COMMENT '密码更新时间',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `user_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '用户类型[1-系统用户 2-注册用户 3-微信用户]',
  `status` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态[0-停用 1-正常]',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'lgyadmin', '超级管理员', '1', '18888888889', '<EMAIL>', 'https://ahimg.miwudalu.com/images/20250625093935_685b5357178b6.png-small', '359b4c7ff41a48a198a451cc464bc749', '127.0.0.1', '2024-11-01 16:42:57', '2023-02-22 11:27:02', 'KOI-ADMIN', '1', '1', '2023-08-06 20:00:00', '2024-08-15 13:45:22', NULL);
INSERT INTO `sys_user` VALUES (3, 'bianji', '111', '1', '111', '', '', 'e7f72eb40201c49fbc24bc2aeec40673', '', NULL, '2025-01-11 10:12:07', '22', '1', '0', '2024-12-13 15:05:54', '2025-01-11 10:12:07', '2025-05-27 20:46:26');

-- ----------------------------
-- Table structure for sys_user_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_dept`;
CREATE TABLE `sys_user_dept`  (
  `user_id` int(20) NOT NULL COMMENT '用户ID',
  `dept_id` int(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`user_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` int(20) NOT NULL COMMENT '用户ID',
  `role_id` int(20) NOT NULL COMMENT '角色ID'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);

SET FOREIGN_KEY_CHECKS = 1;
