![](https://www.thinkphp.cn/uploads/images/20230630/300c856765af4d8ae758c503185f8739.png)

# ThinkPHP 8

[![build](https://github.com/top-think/framework/actions/workflows/build.yml/badge.svg?branch=8.0)](https://github.com/top-think/framework/actions)
[![Total Downloads](https://poser.pugx.org/topthink/framework/downloads)](https://packagist.org/packages/topthink/framework)
[![Latest Stable Version](https://poser.pugx.org/topthink/framework/v/stable)](https://packagist.org/packages/topthink/framework)
[![PHP Version](https://img.shields.io/badge/php-%3E%3D8.0-8892BF.svg)](http://www.php.net/)
[![License](https://poser.pugx.org/topthink/framework/license)](https://packagist.org/packages/topthink/framework)

## 主要特性

- 基于 PHP`8.0+`重构
- 升级`PSR`依赖
- 依赖`think-orm`3.0+ 版本
- 全新的`think-dumper`支持远程调试
- `6.0`/`6.1`无缝升级

> ThinkPHP8 的运行环境要求 PHP8.0+

现在开始，你可以使用官方提供的[ThinkChat](https://chat.topthink.com/)，让你在学习 ThinkPHP 的旅途中享受私人 AI 助理服务！

[![](https://www.topthink.com/uploads/assistant/20230630/4d1a3f0ad2958b49bb8189b7ef824cb0.png)](https://chat.topthink.com/)

ThinkPHP 生态服务由[顶想云](https://www.topthink.com)（TOPThink Cloud）提供，为生态提供专业的开发者服务和价值之选。

## 文档

[完全开发手册](https://doc.thinkphp.cn)

基于官方手册的数据训练和提供精准解答服务
[官方专家智能体](https://chat.topthink.com/chat/eorole)

## 赞助商

全新的[赞助计划](https://www.thinkphp.cn/sponsor)可以让你通过我们的网站、手册、欢迎页及 GIT 仓库获得巨大曝光，同时提升企业的品牌声誉，也更好保障 ThinkPHP 的可持续发展。

[![](https://www.thinkphp.cn/sponsor/special.svg)](https://www.thinkphp.cn/sponsor/special)

[![](https://www.thinkphp.cn/sponsor.svg)](https://www.thinkphp.cn/sponsor)

## 安装

```
composer create-project topthink/think tp
```

启动服务

```
cd tp
php think run
```

然后就可以在浏览器中访问

```
http://localhost:8000
```

如果需要更新框架使用

```
composer update topthink/framework
```

## 命名规范

`ThinkPHP`遵循 PSR-2 命名规范和 PSR-4 自动加载规范。

## 参与开发

直接提交 PR 或者 Issue 即可

## 版权信息

ThinkPHP 遵循 Apache2 开源协议发布，并提供免费使用。

本项目包含的第三方源码和二进制文件之版权信息另行标注。

版权所有 Copyright © 2006-2024 by ThinkPHP (http://thinkphp.cn) All rights reserved。

ThinkPHP® 商标和著作权所有者为上海顶想信息科技有限公司。

更多细节参阅 [LICENSE.txt](LICENSE.txt)
