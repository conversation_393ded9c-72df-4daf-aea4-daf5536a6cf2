# 配置迁移指南

## 概述

本指南说明如何将原来的配置文件（mail.php、cloud_storage.php）迁移到数据库配置系统。

## 数据库表结构

### sys_config 表

```sql
CREATE TABLE `sys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `config_key` varchar(100) NOT NULL DEFAULT '' COMMENT '配置键名',
  `config_value` text COMMENT '配置键值',
  `config_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '配置类型(text:文本,number:数字,boolean:布尔,json:JSON)',
  `config_group` varchar(50) NOT NULL DEFAULT 'system' COMMENT '配置分组(system:系统,mail:邮件,storage:存储)',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `status` char(1) NOT NULL DEFAULT '1' COMMENT '状态(0:停用,1:启用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 迁移步骤

### 1. 执行数据库脚本

```bash
# 执行数据库表创建和初始数据插入
mysql -u username -p database_name < tp8/sql/sys_config_tables.sql
```

### 2. 备份原配置文件（可选）

```bash
# 备份原配置文件
cp tp8/config/mail.php tp8/config/mail_backup.php
cp tp8/config/cloud_storage.php tp8/config/cloud_storage_backup.php
```

### 3. 使用新的配置系统

#### 在代码中读取配置

```php
use app\dogadmin\common\ConfigManager;

// 获取邮件配置
$mailConfig = ConfigManager::getMailConfig();

// 获取存储配置
$storageConfig = ConfigManager::getStorageConfig();

// 获取单个配置值
$smtpHost = ConfigManager::get('mail.host', 'smtp.163.com');
```

#### 更新配置

```php
use app\dogadmin\service\SysConfigService;

$configService = new SysConfigService();

// 更新单个配置
$configService->updateConfig('mail.host', 'smtp.qq.com');

// 批量更新配置
$configs = [
    'mail.host' => 'smtp.qq.com',
    'mail.port' => 587,
    'storage.default' => 'aliyun_oss'
];
$configService->updateConfigs($configs);
```

## API 接口

### 配置管理接口

- `GET /dogadmin/sysConfig/listPage` - 获取配置列表
- `GET /dogadmin/sysConfig/getById` - 获取单个配置
- `POST /dogadmin/sysConfig/add` - 新增配置
- `POST /dogadmin/sysConfig/update` - 更新配置
- `POST /dogadmin/sysConfig/deleteById` - 删除配置
- `POST /dogadmin/sysConfig/updateStatus` - 更新配置状态

### 专用配置接口

- `GET /dogadmin/sysConfig/getMailConfig` - 获取邮件配置
- `GET /dogadmin/sysConfig/getStorageConfig` - 获取存储配置
- `POST /dogadmin/sysConfig/updateMailConfig` - 更新邮件配置
- `POST /dogadmin/sysConfig/updateStorageConfig` - 更新存储配置

## 配置分组

- `system` - 系统配置
- `mail` - 邮件配置
- `storage` - 存储配置

## 配置类型

- `text` - 文本类型
- `number` - 数字类型
- `boolean` - 布尔类型
- `json` - JSON类型

## 缓存机制

配置系统内置了缓存机制，配置更新后会自动清除缓存。如需手动清除缓存：

```php
use app\dogadmin\common\ConfigManager;

// 清除所有配置缓存
ConfigManager::clearCache();

// 清除特定配置缓存
ConfigManager::clearCache('mail_config');

// 刷新配置缓存
ConfigManager::refreshCache();
```

## 注意事项

1. 原配置文件仍然保留作为备用，当数据库读取失败时会使用原配置
2. 配置更新后会自动清除缓存，确保配置实时生效
3. 建议在生产环境中定期备份sys_config表
4. 敏感配置（如密码、密钥）建议加密存储

## 前端管理

访问系统配置管理页面可以通过Web界面管理所有配置项：
- 配置列表查看和搜索
- 配置的增删改查
- 配置状态管理
- 专门的邮件和存储配置管理界面
