<?php

namespace app\dogadmin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\dogadmin\service\SysConfigService;

/**
 * 配置迁移命令
 * 用于将配置文件中的配置迁移到数据库
 */
class ConfigMigrate extends Command
{
    protected function configure()
    {
        $this->setName('config:migrate')
            ->setDescription('Migrate config files to database');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始迁移配置到数据库...');
        
        try {
            $configService = new SysConfigService();
            
            // 迁移邮件配置
            $this->migrateMailConfig($configService, $output);
            
            // 迁移存储配置
            $this->migrateStorageConfig($configService, $output);
            
            $output->writeln('配置迁移完成！');
            
        } catch (\Exception $e) {
            $output->writeln('配置迁移失败：' . $e->getMessage());
        }
    }
    
    /**
     * 迁移邮件配置
     */
    private function migrateMailConfig($configService, $output)
    {
        $output->writeln('正在迁移邮件配置...');
        
        // 读取原配置文件（不通过ConfigManager，直接读取）
        $mailConfigFile = app()->getConfigPath() . 'mail_backup.php';
        if (file_exists($mailConfigFile)) {
            $mailConfig = include $mailConfigFile;
            
            $configs = [];
            foreach ($mailConfig as $key => $value) {
                $configs['mail.' . $key] = $value;
            }
            
            $configService->updateConfigs($configs);
            $output->writeln('邮件配置迁移完成');
        } else {
            $output->writeln('邮件配置文件不存在，跳过迁移');
        }
    }
    
    /**
     * 迁移存储配置
     */
    private function migrateStorageConfig($configService, $output)
    {
        $output->writeln('正在迁移存储配置...');
        
        // 读取原配置文件（不通过ConfigManager，直接读取）
        $storageConfigFile = app()->getConfigPath() . 'cloud_storage_backup.php';
        if (file_exists($storageConfigFile)) {
            $storageConfig = include $storageConfigFile;
            
            $configs = [];
            
            // 处理默认存储类型
            if (isset($storageConfig['default'])) {
                $configs['storage.default'] = $storageConfig['default'];
            }
            
            // 处理各种存储配置
            $storageTypes = ['qiniu', 'tencent_cos', 'aliyun_oss'];
            foreach ($storageTypes as $type) {
                if (isset($storageConfig[$type]) && is_array($storageConfig[$type])) {
                    foreach ($storageConfig[$type] as $key => $value) {
                        $configKey = 'storage.' . $type . '.' . $key;
                        if (is_array($value)) {
                            $configs[$configKey] = json_encode($value, JSON_UNESCAPED_UNICODE);
                        } else {
                            $configs[$configKey] = $value;
                        }
                    }
                }
            }
            
            // 处理上传规则和路径
            if (isset($storageConfig['upload_rules']) && is_array($storageConfig['upload_rules'])) {
                foreach ($storageConfig['upload_rules'] as $key => $value) {
                    $configKey = 'upload_rules.' . $key;
                    if (is_array($value)) {
                        foreach ($value as $subKey => $subValue) {
                            $configs[$configKey . '.' . $subKey] = is_array($subValue) ? json_encode($subValue, JSON_UNESCAPED_UNICODE) : $subValue;
                        }
                    } else {
                        $configs[$configKey] = $value;
                    }
                }
            }
            
            if (isset($storageConfig['upload_paths']) && is_array($storageConfig['upload_paths'])) {
                foreach ($storageConfig['upload_paths'] as $key => $value) {
                    $configKey = 'upload_paths.' . $key;
                    $configs[$configKey] = $value;
                }
            }
            
            $configService->updateConfigs($configs);
            $output->writeln('存储配置迁移完成');
        } else {
            $output->writeln('存储配置文件不存在，跳过迁移');
        }
    }
}
