<?php

namespace app\dogadmin\controller;

use app\dogadmin\service\SysConfigService;
use app\dogadmin\common\ApiResponse;

class SysConfig extends Base
{
    /**
     * @var SysConfigService
     */
    protected $service;
    
    protected function initialize(): void
    {
        $this->service = new SysConfigService();
        $this->params = $this->request->param();
    }
    
    /**
     * 获取配置列表
     * @return \think\Response
     */
    public function listPage()
    {
        $params = $this->params;
        $result = $this->service->listPage($params);
        return ApiResponse::success($result, '获取成功');
    }
    
    /**
     * 根据ID获取配置详情
     * @return \think\Response
     */
    public function getById()
    {
        $params = $this->params;
        if (empty($params['id'])) {
            return ApiResponse::paramError('缺少主键参数id');
        }
        $result = $this->service->getById($params);
        return ApiResponse::success($result, '获取成功');
    }
    
    /**
     * 新增配置
     * @return \think\Response
     */
    public function add()
    {
        $params = $this->params;
        
        // 验证必要参数
        if (empty($params['config_name']) || empty($params['config_key'])) {
            return ApiResponse::paramError('配置名称和配置键不能为空');
        }
        
        $result = $this->service->add($params);
        if ($result) {
            return ApiResponse::success([], '新增成功');
        }
        return ApiResponse::paramError('新增失败');
    }
    
    /**
     * 更新配置
     * @return \think\Response
     */
    public function update()
    {
        $params = $this->params;
        
        if (empty($params['id'])) {
            return ApiResponse::paramError('缺少主键参数id');
        }
        
        $result = $this->service->update($params);
        if ($result) {
            return ApiResponse::success([], '更新成功');
        }
        return ApiResponse::paramError('更新失败');
    }
    
    /**
     * 删除配置
     * @return \think\Response
     */
    public function deleteById()
    {
        $params = $this->params;
        if (empty($params['id'])) {
            return ApiResponse::paramError('缺少主键参数id');
        }
        
        $result = $this->service->deleteById($params);
        if ($result) {
            return ApiResponse::success([], '删除成功');
        }
        return ApiResponse::paramError('删除失败');
    }
    
    /**
     * 批量删除配置
     * @return \think\Response
     */
    public function batchDelete()
    {
        $params = $this->params;
        if (empty($params['ids']) || !is_array($params['ids'])) {
            return ApiResponse::paramError('请选择要删除的数据');
        }
        
        $result = $this->service->batchDelete($params);
        if ($result) {
            return ApiResponse::success([], '批量删除成功');
        }
        return ApiResponse::paramError('批量删除失败');
    }
    
    /**
     * 更新配置状态
     * @return \think\Response
     */
    public function updateStatus()
    {
        $params = $this->params;
        if (empty($params['id']) || !isset($params['status'])) {
            return ApiResponse::paramError('缺少必要参数');
        }
        
        $result = $this->service->updateStatus($params);
        if ($result) {
            return ApiResponse::success([], '状态更新成功');
        }
        return ApiResponse::paramError('状态更新失败');
    }
    
    /**
     * 获取邮件配置
     * @return \think\Response
     */
    public function getMailConfig()
    {
        $result = $this->service->getMailConfig();
        return ApiResponse::success($result, '获取邮件配置成功');
    }
    
    /**
     * 获取存储配置
     * @return \think\Response
     */
    public function getStorageConfig()
    {
        $result = $this->service->getStorageConfig();
        return ApiResponse::success($result, '获取存储配置成功');
    }
    
    /**
     * 更新邮件配置
     * @return \think\Response
     */
    public function updateMailConfig()
    {
        $params = $this->params;
        
        $configs = [];
        foreach ($params as $key => $value) {
            if (in_array($key, ['host', 'port', 'username', 'password', 'from', 'fromName', 'template_dir'])) {
                $configs['mail.' . $key] = $value;
            }
        }
        
        if (empty($configs)) {
            return ApiResponse::paramError('没有有效的配置参数');
        }
        
        $result = $this->service->updateConfigs($configs);
        if ($result) {
            return ApiResponse::success([], '邮件配置更新成功');
        }
        return ApiResponse::paramError('邮件配置更新失败');
    }
    
    /**
     * 更新存储配置
     * @return \think\Response
     */
    public function updateStorageConfig()
    {
        $params = $this->params;
        
        $configs = [];
        
        // 处理默认存储类型
        if (isset($params['default'])) {
            $configs['storage.default'] = $params['default'];
        }
        
        // 处理各种存储配置
        $storageTypes = ['qiniu', 'tencent_cos', 'aliyun_oss'];
        foreach ($storageTypes as $type) {
            if (isset($params[$type]) && is_array($params[$type])) {
                foreach ($params[$type] as $key => $value) {
                    $configKey = 'storage.' . $type . '.' . $key;
                    $configs[$configKey] = $value;
                }
            }
        }
        
        // 处理上传规则和路径
        if (isset($params['upload_rules']) && is_array($params['upload_rules'])) {
            foreach ($params['upload_rules'] as $key => $value) {
                $configKey = 'upload_rules.' . $key;
                $configs[$configKey] = $value;
            }
        }
        
        if (isset($params['upload_paths']) && is_array($params['upload_paths'])) {
            foreach ($params['upload_paths'] as $key => $value) {
                $configKey = 'upload_paths.' . $key;
                $configs[$configKey] = $value;
            }
        }
        
        if (empty($configs)) {
            return ApiResponse::paramError('没有有效的配置参数');
        }
        
        $result = $this->service->updateConfigs($configs);
        if ($result) {
            return ApiResponse::success([], '存储配置更新成功');
        }
        return ApiResponse::paramError('存储配置更新失败');
    }
}
