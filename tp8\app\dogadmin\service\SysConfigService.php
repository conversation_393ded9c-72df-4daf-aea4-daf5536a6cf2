<?php

namespace app\dogadmin\service;

use app\dogadmin\model\SysConfigModel;
use app\dogadmin\common\ConfigManager;

class SysConfigService extends BaseService
{
    public function __construct()
    {
        $this->model = new SysConfigModel();
    }
    
    /**
     * 获取邮件配置
     * @return array
     */
    public function getMailConfig()
    {
        $configs = $this->model->getConfigsByGroup('mail');
        
        // 转换为原配置文件格式
        $mailConfig = [];
        foreach ($configs as $key => $value) {
            $configKey = str_replace('mail.', '', $key);
            $mailConfig[$configKey] = $value;
        }
        
        return $mailConfig;
    }
    
    /**
     * 获取云存储配置
     * @return array
     */
    public function getStorageConfig()
    {
        $configs = $this->model->getConfigsByGroup('storage');
        
        // 构建存储配置数组
        $storageConfig = [
            'default' => $configs['storage.default'] ?? 'qiniu',
            'qiniu' => [],
            'tencent_cos' => [],
            'aliyun_oss' => [],
            'upload_rules' => [],
            'upload_paths' => []
        ];
        
        foreach ($configs as $key => $value) {
            if (strpos($key, 'storage.qiniu.') === 0) {
                $configKey = str_replace('storage.qiniu.', '', $key);
                $this->setNestedValue($storageConfig['qiniu'], $configKey, $value);
            } elseif (strpos($key, 'storage.tencent_cos.') === 0) {
                $configKey = str_replace('storage.tencent_cos.', '', $key);
                $this->setNestedValue($storageConfig['tencent_cos'], $configKey, $value);
            } elseif (strpos($key, 'storage.aliyun_oss.') === 0) {
                $configKey = str_replace('storage.aliyun_oss.', '', $key);
                $this->setNestedValue($storageConfig['aliyun_oss'], $configKey, $value);
            } elseif (strpos($key, 'upload_rules.') === 0) {
                $configKey = str_replace('upload_rules.', '', $key);
                $this->setNestedValue($storageConfig['upload_rules'], $configKey, $value);
            } elseif (strpos($key, 'upload_paths.') === 0) {
                $configKey = str_replace('upload_paths.', '', $key);
                $storageConfig['upload_paths'][str_replace('upload_paths.', '', $key)] = $value;
            }
        }
        
        return $storageConfig;
    }
    
    /**
     * 设置嵌套数组值
     * @param array &$array 目标数组
     * @param string $key 键名（支持点分隔）
     * @param mixed $value 值
     */
    private function setNestedValue(&$array, $key, $value)
    {
        $keys = explode('.', $key);
        $current = &$array;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }
    
    /**
     * 更新配置
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @return bool
     */
    public function updateConfig($key, $value)
    {
        $config = $this->model->where('config_key', $key)->find();
        if (!$config) {
            return false;
        }

        // 根据配置类型处理值
        if ($config['config_type'] === 'json' && is_array($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        } elseif ($config['config_type'] === 'boolean') {
            $value = $value ? '1' : '0';
        }

        $result = $this->model->where('config_key', $key)->update([
            'config_value' => $value,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        // 清除配置缓存
        if ($result) {
            ConfigManager::clearCache();
        }

        return $result;
    }
    
    /**
     * 批量更新配置
     * @param array $configs 配置数组 ['key' => 'value']
     * @return bool
     */
    public function updateConfigs($configs)
    {
        try {
            $this->model->startTrans();
            
            foreach ($configs as $key => $value) {
                $this->updateConfig($key, $value);
            }
            
            $this->model->commit();
            return true;
        } catch (\Exception $exception) {
            $this->model->rollback();
            return false;
        }
    }
    
    /**
     * 获取配置列表（分页）
     * @param array $params 查询参数
     * @param array $searchKey 搜索条件
     * @return array
     */
    public function listPage(array $params, array $searchKey = []): array
    {
        if (empty($searchKey)) {
            $searchKey = [
                ['config_name' => 'LIKE'],
                ['config_key' => 'LIKE'],
                ['config_group' => '='],
                ['status' => '=']
            ];
        }

        return parent::listPage($params, $searchKey);
    }
}
