<?php

namespace app\dogadmin\common;

use app\dogadmin\model\SysConfigModel;

/**
 * 配置管理器
 * 用于从数据库读取配置，替代原来的配置文件
 */
class ConfigManager
{
    /**
     * @var SysConfigModel
     */
    private static $configModel;
    
    /**
     * @var array 配置缓存
     */
    private static $configCache = [];
    
    /**
     * 初始化配置模型
     */
    private static function initModel()
    {
        if (self::$configModel === null) {
            self::$configModel = new SysConfigModel();
        }
    }
    
    /**
     * 获取邮件配置
     * @return array
     */
    public static function getMailConfig()
    {
        $cacheKey = 'mail_config';
        
        if (isset(self::$configCache[$cacheKey])) {
            return self::$configCache[$cacheKey];
        }
        
        self::initModel();
        $configs = self::$configModel->getConfigsByGroup('mail');
        
        // 转换为原配置文件格式
        $mailConfig = [];
        foreach ($configs as $key => $value) {
            $configKey = str_replace('mail.', '', $key);
            $mailConfig[$configKey] = $value;
        }
        
        // 缓存配置
        self::$configCache[$cacheKey] = $mailConfig;
        
        return $mailConfig;
    }
    
    /**
     * 获取云存储配置
     * @return array
     */
    public static function getStorageConfig()
    {
        $cacheKey = 'storage_config';
        
        if (isset(self::$configCache[$cacheKey])) {
            return self::$configCache[$cacheKey];
        }
        
        self::initModel();
        $configs = self::$configModel->getConfigsByGroup('storage');
        
        // 构建存储配置数组
        $storageConfig = [
            'default' => $configs['storage.default'] ?? 'qiniu',
            'qiniu' => [],
            'tencent_cos' => [],
            'aliyun_oss' => [],
            'upload_rules' => [],
            'upload_paths' => []
        ];
        
        foreach ($configs as $key => $value) {
            if (strpos($key, 'storage.qiniu.') === 0) {
                $configKey = str_replace('storage.qiniu.', '', $key);
                self::setNestedValue($storageConfig['qiniu'], $configKey, $value);
            } elseif (strpos($key, 'storage.tencent_cos.') === 0) {
                $configKey = str_replace('storage.tencent_cos.', '', $key);
                self::setNestedValue($storageConfig['tencent_cos'], $configKey, $value);
            } elseif (strpos($key, 'storage.aliyun_oss.') === 0) {
                $configKey = str_replace('storage.aliyun_oss.', '', $key);
                self::setNestedValue($storageConfig['aliyun_oss'], $configKey, $value);
            } elseif (strpos($key, 'upload_rules.') === 0) {
                $configKey = str_replace('upload_rules.', '', $key);
                self::setNestedValue($storageConfig['upload_rules'], $configKey, $value);
            } elseif (strpos($key, 'upload_paths.') === 0) {
                $configKey = str_replace('upload_paths.', '', $key);
                $storageConfig['upload_paths'][str_replace('upload_paths.', '', $key)] = $value;
            }
        }
        
        // 缓存配置
        self::$configCache[$cacheKey] = $storageConfig;
        
        return $storageConfig;
    }
    
    /**
     * 设置嵌套数组值
     * @param array &$array 目标数组
     * @param string $key 键名（支持点分隔）
     * @param mixed $value 值
     */
    private static function setNestedValue(&$array, $key, $value)
    {
        $keys = explode('.', $key);
        $current = &$array;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }
    
    /**
     * 获取单个配置值
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        self::initModel();
        return self::$configModel->getConfigValue($key, $default);
    }
    
    /**
     * 清除配置缓存
     * @param string|null $key 指定清除的缓存键，为null时清除所有缓存
     */
    public static function clearCache($key = null)
    {
        if ($key === null) {
            self::$configCache = [];
        } else {
            unset(self::$configCache[$key]);
        }
    }
    
    /**
     * 刷新配置缓存
     */
    public static function refreshCache()
    {
        self::clearCache();
        // 重新加载常用配置
        self::getMailConfig();
        self::getStorageConfig();
    }
}
