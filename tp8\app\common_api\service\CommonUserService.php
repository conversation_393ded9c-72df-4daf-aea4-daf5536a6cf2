<?php

namespace app\common_api\service;

use app\common_api\model\CommonUserModel;
use app\lib\exception\DebugException;
use app\lib\mylog\MyLog;
use app\lib\service\email\EmailService;
use app\common_api\event\UserRegistered;
use app\dogadmin\service\SysFileService;
use think\facade\Event;

class CommonUserService extends BaseService
{
    public function __construct()
    {
        $this->model = new CommonUserModel();
    }
    //根据用户密码获取用户信息
    public function getTokenByPwd($params)
    {
        $where = [
            ['status', '=', 1],
            ['password', '=', md5_salt($params['password'])],
        ];
        if (!empty($params['mobile'])) {
            $where[] = ['mobile', '=', $params['mobile']];
        }
        if (!empty($params['email'])) {
            $where[] = ['email', '=', $params['email']];
        }
        if (!empty($params['username'])) {
            $where[] = ['username', '=', $params['username']];
        }
        return $this->model->where($where)->find();
    }

    public function getUserInfo($params){
        $where = [
            ['status', '=', 1],
            ['id','=',$params['user_id']]
        ];
        return $this->model->where($where)->find();
    }
    //注册用户
    public function register($params){
        //先查询用户是否存在
        $where = [
            ['status', '=', 1],
            ['username', '=', $params['username']],
        ];
        $user = $this->model->where($where)->find();
        if($user){
            return false;
        }
        $data = [
            'nickname'=> '用户'.generateRandomString(),
            'username' => $params['username'],
            'password' => md5_salt($params['password']),
            'status' => 1
        ];
        $data = $this->addTime($data,['create_time','update_time']);
        $userId = $this->model->insertGetId($data);
        
        // 触发用户注册成功事件
        if ($userId) {
            Event::trigger(new UserRegistered($userId, $data, 'normal'));
        }
        
        return $userId;
    }

    //邮箱注册
    public function emailRegister($params){
        //验证邮箱是否已存在
        $where = [
            ['status', '=', 1],
            ['email', '=', $params['email']],
        ];
        $user = $this->model->where($where)->find();
        if($user){
            return false;
        }

        $data = [
            'nickname'=> '用户'.generateRandomString(),
            'email' => $params['email'],
            'username' => $params['email'],
            'password' => md5_salt($params['password']),
            'status' => 1
        ];

        $data = $this->addTime($data,['create_time','update_time']);
        //如果有用户名参数，也保存
        if(!empty($params['username'])){
            $data['username'] = $params['username'];
        }
        
        $userId = $this->model->insertGetId($data);
        
        // 触发用户注册成功事件
        if ($userId) {
            Event::trigger(new UserRegistered($userId, $data, 'email'));
        }
        
        return $userId;
    }

    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @return bool 发送结果
     */
    public function sendEmail($to, $subject, $content) {
        try {
            $emailService = new EmailService();
            return $emailService->sendHtml($to, $subject, $content);
        } catch (\Exception $e) {
            // 记录错误日志
            MyLog::error('邮件发送失败: ' . $e->getMessage());
            return false;
        }
    }

    //根据用户id获取用户信息
    public function getUserInfoById($userId){
        $where = [
            ['status', '=', 1],
            ['id','=',$userId]
        ];
        return $this->model->where($where)->find();
    }

    /**
     * 忘记密码-重置密码
     * @param array $params
     * @return true|string  成功返回true，失败返回错误信息
     */
    public function resetPassword($params) {
        try {
            $user = $this->model->where([
                ['status', '=', 1],
                ['email', '=', $params['email']]
            ])->find();
            if (!$user) {
                return '用户不存在';
            }
            if ($user['password'] === md5_salt($params['password'])) {
                return '新密码不能与旧密码相同';
            }
            $data = [
                'password' => md5_salt($params['password']),
                'update_time' => date('Y-m-d H:i:s')
            ];
            $result = $this->model->where([
                ['status', '=', 1],
                ['email', '=', $params['email']]
            ])->update($data);
            return $result !== false ? true : '密码重置失败';
        } catch (\Exception $e) {
            MyLog::error('重置密码失败: ' . $e->getMessage());
            return '密码重置异常';
        }
    }

    /**
     * 修改密码
     * @param array $params
     * @return true|string  成功返回true，失败返回错误信息
     */
    public function changePassword($params) {
        try {
            $user = $this->model->where([
                ['status', '=', 1],
                ['id', '=', $params['user_id']]
            ])->find();
            if (!$user) {
                return '用户不存在';
            }
            if ($user['password'] !== md5_salt($params['old_password'])) {
                return '原密码错误';
            }
            if ($params['old_password'] === $params['new_password']) {
                return '新密码不能与原密码相同';
            }
            $data = [
                'password' => md5_salt($params['new_password']),
                'update_time' => date('Y-m-d H:i:s')
            ];
            $result = $this->model->where([
                ['status', '=', 1],
                ['id', '=', $params['user_id']]
            ])->update($data);
            return $result !== false ? true : '密码修改失败';
        } catch (\Exception $e) {
            MyLog::error('修改密码失败: ' . $e->getMessage());
            return '密码修改异常';
        }
    }

    /**
     * 修改用户昵称
     * @param array $params
     * @return bool
     */
    public function updateNickname($params) {
        try {
            $where = [
                ['status', '=', 1],
                ['id', '=', $params['user_id']]
            ];
            
            $data = [
                'nickname' => $params['nickname'],
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            $result = $this->model->where($where)->update($data);
            return $result !== false;
            
        } catch (\Exception $e) {
            MyLog::error('修改用户昵称失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 上传用户头像
     * @param \think\File $file
     * @param int $userId
     * @return array|false
     */
    public function uploadAvatar($file, $userId, $extension) {
        try {
            // 使用SysFileService上传文件
            $fileService = new SysFileService();
            
            // 设置上传选项
            $options = [
                'upload_path' => 'avatars/',
                'fileName' => 'avatar_' . $userId . '.' . $extension
            ];
            
            // 上传到云存储
            $uploadResult = $fileService->uploadToCloud($file, '', 'image', $options, false);
            
            if (!$uploadResult['success']) {
                return false;
            }
            
            // 更新用户头像URL
            $avatarUrl = $uploadResult['file_info']['file_path'];
            $where = [
                ['status', '=', 1],
                ['id', '=', $userId]
            ];
            
            $data = [
                'avatar_url' => $avatarUrl,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            $updateResult = $this->model->where($where)->update($data);
            
            if ($updateResult !== false) {
                return [
                    'avatar_url' => $avatarUrl,
                    'file_id' => $uploadResult['file_id'],
                    'file_info' => $uploadResult['file_info']
                ];
            }
            
            return false;
            
        } catch (\Exception $e) {
            MyLog::error('上传用户头像失败: ' . $e->getMessage());
            return false;
        }
    }

}