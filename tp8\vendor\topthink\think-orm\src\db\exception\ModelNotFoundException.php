<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2025 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 麦当苗儿 <<EMAIL>> <http://zjzit.cn>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace think\db\exception;

class ModelNotFoundException extends DbException
{
    protected $model;

    /**
     * 构造方法.
     *
     * @param string $message
     * @param string $model
     * @param array  $config
     */
    public function __construct(string $message, string $model = '', array $config = [])
    {
        $this->message = $message;
        $this->model = $model;

        $this->setData('Database Config', $config);
    }

    /**
     * 获取模型类名.
     *
     * @return string
     */
    public function getModel()
    {
        return $this->model;
    }
}
