<template>
  <div class="koi-container">
    <el-card shadow="hover" class="koi-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" class="koi-form">
        <el-form-item label="配置名称" prop="config_name">
          <el-input v-model="queryParams.config_name" placeholder="请输入配置名称" clearable />
        </el-form-item>
        <el-form-item label="配置键" prop="config_key">
          <el-input v-model="queryParams.config_key" placeholder="请输入配置键" clearable />
        </el-form-item>
        <el-form-item label="配置分组" prop="config_group">
          <el-select v-model="queryParams.config_group" placeholder="请选择配置分组" clearable>
            <el-option label="系统配置" value="system" />
            <el-option label="邮件配置" value="mail" />
            <el-option label="存储配置" value="storage" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格头部按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Plus" @click="handleAdd">新增配置</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" icon="Setting" @click="handleMailConfig">邮件配置</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" icon="Upload" @click="handleStorageConfig">存储配置</el-button>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="configList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="配置ID" prop="id" width="80" />
        <el-table-column label="配置名称" prop="config_name" :show-overflow-tooltip="true" />
        <el-table-column label="配置键" prop="config_key" :show-overflow-tooltip="true" />
        <el-table-column label="配置值" prop="config_value" :show-overflow-tooltip="true" width="200" />
        <el-table-column label="配置类型" prop="config_type" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.config_type === 'text'" type="primary">文本</el-tag>
            <el-tag v-else-if="scope.row.config_type === 'number'" type="success">数字</el-tag>
            <el-tag v-else-if="scope.row.config_type === 'boolean'" type="warning">布尔</el-tag>
            <el-tag v-else-if="scope.row.config_type === 'json'" type="info">JSON</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配置分组" prop="config_group" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.config_group === 'system'" type="primary">系统</el-tag>
            <el-tag v-else-if="scope.row.config_group === 'mail'" type="success">邮件</el-tag>
            <el-tag v-else-if="scope.row.config_group === 'storage'" type="warning">存储</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" />
        <el-table-column label="创建时间" prop="create_time" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" icon="Edit" size="small" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button type="danger" icon="Delete" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.page_no"
        v-model:limit="queryParams.page_size"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="配置名称" prop="config_name">
          <el-input v-model="form.config_name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键" prop="config_key">
          <el-input v-model="form.config_key" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="配置值" prop="config_value">
          <el-input
            v-if="form.config_type !== 'json'"
            v-model="form.config_value"
            :type="form.config_type === 'number' ? 'number' : 'text'"
            placeholder="请输入配置值"
          />
          <el-input
            v-else
            v-model="form.config_value"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的配置值"
          />
        </el-form-item>
        <el-form-item label="配置类型" prop="config_type">
          <el-select v-model="form.config_type" placeholder="请选择配置类型">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="布尔" value="boolean" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置分组" prop="config_group">
          <el-select v-model="form.config_group" placeholder="请选择配置分组">
            <el-option label="系统配置" value="system" />
            <el-option label="邮件配置" value="mail" />
            <el-option label="存储配置" value="storage" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="configPage">
import { nextTick, ref, reactive, onMounted } from "vue";
import {
  koiNoticeSuccess,
  koiNoticeError,
  koiMsgInfo,
  koiMsgSuccess,
  koiMsgWarning,
  koiMsgError,
  koiMsgBox
} from "@/utils/koi.ts";
import {
  listPage,
  getById,
  add,
  update,
  deleteById,
  batchDelete,
  updateStatus
} from "@/api/system/config/index.ts";

const { proxy } = getCurrentInstance() as any;

const configList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const queryParams = ref({
  page_no: 1,
  page_size: 10,
  config_name: "",
  config_key: "",
  config_group: "",
  status: ""
});

const form = ref({
  id: undefined,
  config_name: "",
  config_key: "",
  config_value: "",
  config_type: "text",
  config_group: "system",
  status: "1",
  remark: ""
});

const rules = reactive({
  config_name: [{ required: true, message: "配置名称不能为空", trigger: "blur" }],
  config_key: [{ required: true, message: "配置键不能为空", trigger: "blur" }],
  config_value: [{ required: true, message: "配置值不能为空", trigger: "blur" }],
  config_type: [{ required: true, message: "配置类型不能为空", trigger: "change" }],
  config_group: [{ required: true, message: "配置分组不能为空", trigger: "change" }]
});

/** 查询配置列表 */
function getList() {
  loading.value = true;
  listPage(queryParams.value).then(response => {
    configList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    config_name: "",
    config_key: "",
    config_value: "",
    config_type: "text",
    config_group: "system",
    status: "1",
    remark: ""
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page_no = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryFormRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加配置";
}

/** 修改按钮操作 */
function handleUpdate(row?: any) {
  reset();
  const id = row?.id || ids.value[0];
  getById(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid: any) => {
    if (valid) {
      if (form.value.id != undefined) {
        update(form.value).then(response => {
          koiMsgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        add(form.value).then(response => {
          koiMsgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const configIds = row?.id || ids.value;
  koiMsgBox('是否确认删除配置编号为"' + configIds + '"的数据项？').then(function () {
    return deleteById(configIds);
  }).then(() => {
    getList();
    koiMsgSuccess("删除成功");
  }).catch(() => {});
}

/** 状态修改 */
function handleStatusChange(row: any) {
  let text = row.status === "1" ? "启用" : "停用";
  koiMsgBox('确认要"' + text + '""' + row.config_name + '"配置吗？').then(function () {
    return updateStatus(row.id, row.status);
  }).then(() => {
    koiMsgSuccess(text + "成功");
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0";
  });
}

/** 行点击事件 */
function handleRowClick(row: any) {
  // 可以在这里添加行点击逻辑
}

/** 邮件配置 */
function handleMailConfig() {
  // 跳转到邮件配置页面或打开邮件配置对话框
  koiMsgInfo("邮件配置功能开发中...");
}

/** 存储配置 */
function handleStorageConfig() {
  // 跳转到存储配置页面或打开存储配置对话框
  koiMsgInfo("存储配置功能开发中...");
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.koi-container {
  padding: 20px;
}

.koi-card {
  margin-bottom: 20px;
}

.koi-form {
  margin-bottom: 20px;
}
</style>
