<?php
use app\dogadmin\common\ConfigManager;

// 从数据库读取邮件配置
try {
    $mailConfig = ConfigManager::getMailConfig();
    return $mailConfig;
} catch (\Exception $e) {
    // 如果数据库读取失败，返回默认配置
    return [
        'host' => 'smtp.163.com',
        'port' => 465,
        'username' => '<EMAIL>',
        'password' => 'WHBGTPDKCBCGCPAN',
        'from' => '<EMAIL>',
        'fromName' => '来自注册的邮件',
        'template_dir' => 'path/to/email/templates'
    ];
}